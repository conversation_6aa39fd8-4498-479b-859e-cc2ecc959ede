const mongoose = require('mongoose');

const predictionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  stockSymbol: {
    type: String,
    required: true,
    uppercase: true,
    trim: true
  },
  exchange: {
    type: String,
    required: true,
    enum: ['NSE', 'BSE'],
    default: 'NSE'
  },
  predictionType: {
    type: String,
    required: true,
    enum: ['short_term', 'medium_term', 'long_term'],
    default: 'short_term'
  },
  timeframe: {
    type: String,
    required: true,
    enum: ['1d', '1w', '1m', '3m', '6m', '1y'],
    default: '1d'
  },
  currentPrice: {
    type: Number,
    required: true
  },
  predictedPrice: {
    type: Number,
    required: true
  },
  confidence: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  direction: {
    type: String,
    required: true,
    enum: ['bullish', 'bearish', 'neutral']
  },
  expectedReturn: {
    type: Number,
    required: true
  },
  expectedReturnPercent: {
    type: Number,
    required: true
  },
  riskLevel: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high']
  },
  technicalIndicators: {
    rsi: Number,
    macd: Number,
    sma20: Number,
    sma50: Number,
    bollinger: {
      upper: Number,
      middle: Number,
      lower: Number
    },
    support: Number,
    resistance: Number
  },
  fundamentalFactors: {
    peRatio: Number,
    pbRatio: Number,
    debtToEquity: Number,
    roe: Number,
    eps: Number,
    marketCap: Number
  },
  marketSentiment: {
    type: String,
    enum: ['very_positive', 'positive', 'neutral', 'negative', 'very_negative'],
    default: 'neutral'
  },
  newsImpact: {
    type: String,
    enum: ['positive', 'neutral', 'negative'],
    default: 'neutral'
  },
  modelUsed: {
    type: String,
    required: true,
    default: 'MCP_AI_Model'
  },
  modelVersion: {
    type: String,
    default: '1.0'
  },
  accuracy: {
    type: Number,
    min: 0,
    max: 100
  },
  predictionDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  targetDate: {
    type: Date,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  actualPrice: {
    type: Number
  },
  actualReturn: {
    type: Number
  },
  predictionAccuracy: {
    type: Number
  },
  status: {
    type: String,
    enum: ['pending', 'active', 'completed', 'expired'],
    default: 'active'
  }
}, {
  timestamps: true
});

// Indexes for better query performance
predictionSchema.index({ stockSymbol: 1, exchange: 1 });
predictionSchema.index({ predictionDate: -1 });
predictionSchema.index({ targetDate: 1 });
predictionSchema.index({ status: 1 });
predictionSchema.index({ predictionType: 1 });
predictionSchema.index({ userId: 1 });

// Virtual for days until target
predictionSchema.virtual('daysUntilTarget').get(function () {
  const now = new Date();
  const target = new Date(this.targetDate);
  const diffTime = target - now;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Method to calculate prediction accuracy
predictionSchema.methods.calculateAccuracy = function () {
  if (this.actualPrice && this.predictedPrice && this.currentPrice) {
    const predictedChange = this.predictedPrice - this.currentPrice;
    const actualChange = this.actualPrice - this.currentPrice;

    // Calculate accuracy based on direction and magnitude
    const directionAccuracy = (predictedChange * actualChange) > 0 ? 50 : 0;
    const magnitudeAccuracy = Math.max(0, 50 - Math.abs(predictedChange - actualChange) / this.currentPrice * 100);

    this.predictionAccuracy = directionAccuracy + magnitudeAccuracy;
    this.actualReturn = actualChange;
    this.status = 'completed';

    return this.predictionAccuracy;
  }
  return null;
};

// Static method to get active predictions for a stock
predictionSchema.statics.getActivePredictions = function (symbol, exchange = 'NSE') {
  return this.find({
    stockSymbol: symbol.toUpperCase(),
    exchange,
    status: 'active',
    targetDate: { $gte: new Date() }
  }).sort({ predictionDate: -1 });
};

// Static method to get prediction statistics
predictionSchema.statics.getStats = function (symbol, exchange = 'NSE') {
  return this.aggregate([
    {
      $match: {
        stockSymbol: symbol.toUpperCase(),
        exchange,
        status: 'completed',
        predictionAccuracy: { $exists: true }
      }
    },
    {
      $group: {
        _id: null,
        avgAccuracy: { $avg: '$predictionAccuracy' },
        totalPredictions: { $sum: 1 },
        successfulPredictions: {
          $sum: { $cond: [{ $gte: ['$predictionAccuracy', 70] }, 1, 0] }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Prediction', predictionSchema);
