const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  preferences: {
    watchlist: [{
      symbol: String,
      exchange: {
        type: String,
        enum: ['NSE', 'BSE'],
        default: 'NSE'
      },
      addedAt: {
        type: Date,
        default: Date.now
      }
    }],
    defaultExchange: {
      type: String,
      enum: ['NSE', 'BSE'],
      default: 'NSE'
    },
    riskTolerance: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    investmentHorizon: {
      type: String,
      enum: ['short_term', 'medium_term', 'long_term'],
      default: 'medium_term'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      priceAlerts: {
        type: Boolean,
        default: true
      },
      predictionUpdates: {
        type: Boolean,
        default: true
      }
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium'],
      default: 'free'
    },
    startDate: Date,
    endDate: Date,
    isActive: {
      type: Boolean,
      default: true
    }
  },
  apiUsage: {
    dailyRequests: {
      type: Number,
      default: 0
    },
    lastResetDate: {
      type: Date,
      default: Date.now
    },
    totalRequests: {
      type: Number,
      default: 0
    }
  },
  lastLogin: Date,
  isActive: {
    type: Boolean,
    default: true
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  verificationToken: String,
  resetPasswordToken: String,
  resetPasswordExpires: Date,
  refreshToken: String
}, {
  timestamps: true
});

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ 'preferences.watchlist.symbol': 1 });

// Virtual for full name
userSchema.virtual('fullName').get(function () {
  return `${this.firstName} ${this.lastName}`;
});

// Hash password before saving
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function (candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Add stock to watchlist
userSchema.methods.addToWatchlist = function (symbol, exchange = 'NSE') {
  const exists = this.preferences.watchlist.some(
    item => item.symbol === symbol.toUpperCase() && item.exchange === exchange
  );

  if (!exists) {
    this.preferences.watchlist.push({
      symbol: symbol.toUpperCase(),
      exchange,
      addedAt: new Date()
    });
  }

  return this.save();
};

// Remove stock from watchlist
userSchema.methods.removeFromWatchlist = function (symbol, exchange = 'NSE') {
  this.preferences.watchlist = this.preferences.watchlist.filter(
    item => !(item.symbol === symbol.toUpperCase() && item.exchange === exchange)
  );

  return this.save();
};

// Check API rate limits
userSchema.methods.checkRateLimit = function () {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  // Reset daily counter if it's a new day
  if (this.apiUsage.lastResetDate < today) {
    this.apiUsage.dailyRequests = 0;
    this.apiUsage.lastResetDate = today;
  }

  // Check limits based on subscription plan
  const limits = {
    free: 100,
    basic: 1000,
    premium: 10000
  };

  const limit = limits[this.subscription.plan] || limits.free;
  return this.apiUsage.dailyRequests < limit;
};

// Increment API usage
userSchema.methods.incrementApiUsage = function () {
  this.apiUsage.dailyRequests += 1;
  this.apiUsage.totalRequests += 1;
  return this.save();
};

module.exports = mongoose.model('User', userSchema);
