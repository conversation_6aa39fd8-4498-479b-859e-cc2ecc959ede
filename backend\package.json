{"name": "indian-stock-prediction-backend", "version": "1.0.0", "description": "Node.js backend for Indian stock market prediction using MCP", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["stock", "prediction", "indian-market", "nse", "bse", "mcp", "ai"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "node-cron": "^3.0.2", "ws": "^8.13.0", "yahoo-finance2": "^2.4.3"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}