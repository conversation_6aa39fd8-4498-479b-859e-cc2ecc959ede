const express = require('express');
const predictionService = require('../services/predictionService');
const Prediction = require('../models/Prediction');
const { auth, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Generate new prediction
router.post('/generate', auth, async (req, res) => {
  try {
    const { symbol, exchange = 'NSE', timeframe = '1d', predictionType = 'short_term' } = req.body;

    if (!symbol) {
      return res.status(400).json({
        success: false,
        message: 'Stock symbol is required'
      });
    }

    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    // Validate timeframe
    const validTimeframes = ['1d', '1w', '1m', '3m', '6m', '1y'];
    if (!validTimeframes.includes(timeframe)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid timeframe. Valid options: ' + validTimeframes.join(', ')
      });
    }

    // Validate prediction type
    const validTypes = ['short_term', 'medium_term', 'long_term'];
    if (!validTypes.includes(predictionType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid prediction type. Valid options: ' + validTypes.join(', ')
      });
    }

    const prediction = await predictionService.generatePrediction(
      symbol,
      exchange,
      timeframe,
      predictionType,
      req.user.userId
    );

    res.status(201).json({
      success: true,
      message: 'Prediction generated successfully',
      data: prediction
    });

  } catch (error) {
    console.error('Prediction generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate prediction',
      error: error.message
    });
  }
});

// Get predictions for a stock
router.get('/:symbol', optionalAuth, async (req, res) => {
  try {
    const { symbol } = req.params;
    const { exchange = 'NSE', limit = 10, type, status } = req.query;

    let query = {
      stockSymbol: symbol.toUpperCase(),
      exchange,
      isActive: true
    };

    if (type) {
      query.predictionType = type;
    }

    if (status) {
      query.status = status;
    }

    const predictions = await Prediction.find(query)
      .sort({ predictionDate: -1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: predictions,
      count: predictions.length
    });

  } catch (error) {
    console.error('Predictions fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch predictions',
      error: error.message
    });
  }
});

// Get active predictions for a stock
router.get('/:symbol/active', optionalAuth, async (req, res) => {
  try {
    const { symbol } = req.params;
    const { exchange = 'NSE' } = req.query;

    const predictions = await Prediction.getActivePredictions(symbol, exchange);

    res.json({
      success: true,
      data: predictions,
      count: predictions.length
    });

  } catch (error) {
    console.error('Active predictions fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch active predictions',
      error: error.message
    });
  }
});

// Get prediction statistics for a stock
router.get('/:symbol/stats', optionalAuth, async (req, res) => {
  try {
    const { symbol } = req.params;
    const { exchange = 'NSE' } = req.query;

    const stats = await predictionService.getPredictionStats(symbol, exchange);

    res.json({
      success: true,
      data: {
        symbol: symbol.toUpperCase(),
        exchange,
        ...stats,
        successRate: stats.totalPredictions > 0
          ? (stats.successfulPredictions / stats.totalPredictions * 100).toFixed(2)
          : 0
      }
    });

  } catch (error) {
    console.error('Prediction stats fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch prediction statistics',
      error: error.message
    });
  }
});

// Update prediction with actual result
router.put('/:id/result', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { actualPrice } = req.body;

    if (!actualPrice || typeof actualPrice !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'Valid actual price is required'
      });
    }

    const prediction = await predictionService.updatePredictionResult(id, actualPrice);

    res.json({
      success: true,
      message: 'Prediction result updated successfully',
      data: {
        id: prediction._id,
        accuracy: prediction.predictionAccuracy,
        actualReturn: prediction.actualReturn,
        status: prediction.status
      }
    });

  } catch (error) {
    console.error('Prediction update error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update prediction result',
      error: error.message
    });
  }
});

// Batch generate predictions for multiple stocks
router.post('/batch', auth, async (req, res) => {
  try {
    const { stocks, timeframe = '1d', predictionType = 'short_term' } = req.body;

    if (!stocks || !Array.isArray(stocks) || stocks.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Array of stocks is required'
      });
    }

    if (stocks.length > 10) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 10 stocks allowed per batch request'
      });
    }

    const results = await predictionService.batchGeneratePredictions(stocks, timeframe, req.user.userId);

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    res.json({
      success: true,
      message: `Generated predictions for ${successful.length} out of ${stocks.length} stocks`,
      data: {
        successful: successful.map(r => r.prediction),
        failed: failed.map(r => ({ symbol: r.symbol, error: r.error })),
        summary: {
          total: stocks.length,
          successful: successful.length,
          failed: failed.length
        }
      }
    });

  } catch (error) {
    console.error('Batch prediction error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate batch predictions',
      error: error.message
    });
  }
});

// Get recent predictions across all stocks
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { limit = 20, type, status, exchange } = req.query;

    let query = { isActive: true };

    if (type) {
      query.predictionType = type;
    }

    if (status) {
      query.status = status;
    }

    if (exchange) {
      query.exchange = exchange;
    }

    const predictions = await Prediction.find(query)
      .sort({ predictionDate: -1 })
      .limit(parseInt(limit))
      .select('stockSymbol exchange predictionType timeframe currentPrice predictedPrice confidence direction expectedReturnPercent riskLevel predictionDate targetDate status');

    res.json({
      success: true,
      data: predictions,
      count: predictions.length
    });

  } catch (error) {
    console.error('Recent predictions fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent predictions',
      error: error.message
    });
  }
});

// Get prediction by ID
router.get('/id/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const prediction = await Prediction.findById(id);

    if (!prediction) {
      return res.status(404).json({
        success: false,
        message: 'Prediction not found'
      });
    }

    res.json({
      success: true,
      data: prediction
    });

  } catch (error) {
    console.error('Prediction fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch prediction',
      error: error.message
    });
  }
});

// Get all predictions for authenticated user
router.get('/all', auth, async (req, res) => {
  try {
    const { limit = 50, type, status, exchange } = req.query;

    let query = { userId: req.user.userId };

    if (type) {
      query.predictionType = type;
    }

    if (status) {
      query.status = status;
    }

    if (exchange) {
      query.exchange = exchange;
    }

    const predictions = await Prediction.find(query)
      .sort({ predictionDate: -1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: predictions,
      count: predictions.length
    });

  } catch (error) {
    console.error('All predictions fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch all predictions',
      error: error.message
    });
  }
});

// Get active predictions for authenticated user
router.get('/active', auth, async (req, res) => {
  try {
    const { exchange } = req.query;

    let query = {
      userId: req.user.userId,
      status: 'active',
      isActive: true
    };

    if (exchange) {
      query.exchange = exchange;
    }

    const predictions = await Prediction.find(query)
      .sort({ predictionDate: -1 });

    res.json({
      success: true,
      data: predictions,
      count: predictions.length
    });

  } catch (error) {
    console.error('Active predictions fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch active predictions',
      error: error.message
    });
  }
});

// Get completed predictions for authenticated user
router.get('/completed', auth, async (req, res) => {
  try {
    const { exchange } = req.query;

    let query = {
      userId: req.user.userId,
      status: { $in: ['completed', 'expired'] }
    };

    if (exchange) {
      query.exchange = exchange;
    }

    const predictions = await Prediction.find(query)
      .sort({ predictionDate: -1 });

    res.json({
      success: true,
      data: predictions,
      count: predictions.length
    });

  } catch (error) {
    console.error('Completed predictions fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch completed predictions',
      error: error.message
    });
  }
});

// Refresh prediction
router.put('/:id/refresh', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const prediction = await Prediction.findOne({ _id: id, userId: req.user.userId });

    if (!prediction) {
      return res.status(404).json({
        success: false,
        message: 'Prediction not found'
      });
    }

    // Generate new prediction with current data
    const newPrediction = await predictionService.generatePrediction(
      prediction.stockSymbol,
      prediction.exchange,
      prediction.timeframe,
      prediction.predictionType,
      req.user.userId
    );

    // Update the existing prediction
    prediction.currentPrice = newPrediction.currentPrice;
    prediction.predictedPrice = newPrediction.predictedPrice;
    prediction.confidence = newPrediction.confidence;
    prediction.direction = newPrediction.direction;
    prediction.expectedReturn = newPrediction.expectedReturn;
    prediction.expectedReturnPercent = newPrediction.expectedReturnPercent;
    prediction.riskLevel = newPrediction.riskLevel;
    prediction.technicalIndicators = newPrediction.technicalIndicators;
    prediction.marketSentiment = newPrediction.marketSentiment;
    prediction.lastUpdated = new Date();

    await prediction.save();

    res.json({
      success: true,
      message: 'Prediction refreshed successfully',
      data: prediction
    });

  } catch (error) {
    console.error('Prediction refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to refresh prediction',
      error: error.message
    });
  }
});

// Delete prediction
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const prediction = await Prediction.findOne({ _id: id, userId: req.user.userId });

    if (!prediction) {
      return res.status(404).json({
        success: false,
        message: 'Prediction not found'
      });
    }

    await Prediction.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Prediction deleted successfully'
    });

  } catch (error) {
    console.error('Prediction deletion error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete prediction',
      error: error.message
    });
  }
});

module.exports = router;
