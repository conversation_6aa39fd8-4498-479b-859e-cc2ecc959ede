const express = require('express');
const stockService = require('../services/stockService');
const Stock = require('../models/Stock');
const { auth, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Search stocks
router.get('/search', optionalAuth, async (req, res) => {
  try {
    const { q: query, exchange = 'NSE' } = req.query;

    if (!query || query.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters long'
      });
    }

    const results = await stockService.searchStocks(query, exchange);

    res.json({
      success: true,
      data: results,
      count: results.length
    });

  } catch (error) {
    console.error('Stock search error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search stocks',
      error: error.message
    });
  }
});

// Get stock current price and basic info
router.get('/:symbol/current', optionalAuth, async (req, res) => {
  try {
    const { symbol } = req.params;
    const { exchange = 'NSE' } = req.query;

    const currentData = await stockService.getCurrentPrice(symbol, exchange);

    res.json({
      success: true,
      data: currentData
    });

  } catch (error) {
    console.error('Current price fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch current price',
      error: error.message
    });
  }
});

// Get stock historical data
router.get('/:symbol/historical', optionalAuth, async (req, res) => {
  try {
    const { symbol } = req.params;
    const { exchange = 'NSE', period = '1y' } = req.query;

    const historicalData = await stockService.getHistoricalData(symbol, exchange, period);

    res.json({
      success: true,
      data: historicalData,
      count: historicalData.length,
      period
    });

  } catch (error) {
    console.error('Historical data fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch historical data',
      error: error.message
    });
  }
});

// Get stock details with technical indicators
router.get('/:symbol/details', optionalAuth, async (req, res) => {
  try {
    const { symbol } = req.params;
    const { exchange = 'NSE' } = req.query;

    // Get current data
    const currentData = await stockService.getCurrentPrice(symbol, exchange);

    // Get historical data for technical analysis
    const historicalData = await stockService.getHistoricalData(symbol, exchange, '1y');

    // Calculate technical indicators
    const technicalIndicators = stockService.calculateTechnicalIndicators(historicalData);

    // Get or create stock in database
    let stock = await Stock.findBySymbol(symbol, exchange);
    if (!stock) {
      stock = await stockService.updateStockData(symbol, exchange);
    }

    res.json({
      success: true,
      data: {
        ...currentData,
        technicalIndicators,
        historicalDataCount: historicalData.length,
        lastUpdated: stock.lastUpdated
      }
    });

  } catch (error) {
    console.error('Stock details fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch stock details',
      error: error.message
    });
  }
});

// Get top gainers/losers
router.get('/movers/:type', optionalAuth, async (req, res) => {
  try {
    const { type } = req.params; // 'gainers' or 'losers'
    const { limit = 10 } = req.query;

    if (!['gainers', 'losers'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Type must be either "gainers" or "losers"'
      });
    }

    const movers = await stockService.getTopMovers(type, parseInt(limit));

    res.json({
      success: true,
      data: movers,
      type,
      count: movers.length
    });

  } catch (error) {
    console.error('Top movers fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch top movers',
      error: error.message
    });
  }
});

// Get user's watchlist stocks data
router.get('/watchlist/data', auth, async (req, res) => {
  try {
    const user = req.user;

    // Get user's full data to access watchlist
    const userData = await require('../models/User').findById(user.userId);
    const watchlist = userData.preferences.watchlist || [];

    if (watchlist.length === 0) {
      return res.json({
        success: true,
        data: [],
        message: 'Watchlist is empty'
      });
    }

    // Fetch current data for all watchlist stocks
    const watchlistData = await Promise.allSettled(
      watchlist.map(async (item) => {
        try {
          const currentData = await stockService.getCurrentPrice(item.symbol, item.exchange);
          return {
            ...currentData,
            addedAt: item.addedAt
          };
        } catch (error) {
          return {
            symbol: item.symbol,
            exchange: item.exchange,
            error: error.message,
            addedAt: item.addedAt
          };
        }
      })
    );

    const successfulData = watchlistData
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);

    const failedData = watchlistData
      .filter(result => result.status === 'rejected')
      .map(result => result.reason);

    res.json({
      success: true,
      data: successfulData,
      errors: failedData,
      count: successfulData.length
    });

  } catch (error) {
    console.error('Watchlist data fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch watchlist data',
      error: error.message
    });
  }
});

// Update stock data in database
router.post('/:symbol/update', auth, async (req, res) => {
  try {
    const { symbol } = req.params;
    const { exchange = 'NSE' } = req.body;

    const stock = await stockService.updateStockData(symbol, exchange);

    res.json({
      success: true,
      message: 'Stock data updated successfully',
      data: {
        symbol: stock.symbol,
        exchange: stock.exchange,
        currentPrice: stock.currentPrice,
        lastUpdated: stock.lastUpdated,
        historicalDataPoints: stock.historicalData.length
      }
    });

  } catch (error) {
    console.error('Stock update error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update stock data',
      error: error.message
    });
  }
});

// Get popular/trending stocks
router.get('/popular', optionalAuth, async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    // Get popular Indian stocks (NIFTY 50 top companies)
    const popularStocks = [
      'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
      'ICICIBANK', 'KOTAKBANK', 'BHARTIARTL', 'ITC', 'SBIN',
      'LT', 'ASIANPAINT', 'MARUTI', 'TITAN', 'WIPRO',
      'NESTLEIND', 'HCLTECH', 'BAJFINANCE', 'POWERGRID', 'NTPC',
      'AXISBANK', 'ULTRACEMCO', 'ONGC', 'SUNPHARMA', 'TECHM',
      'TATAMOTORS', 'COALINDIA', 'INDUSINDBK', 'ADANIPORTS', 'JSWSTEEL'
    ].slice(0, parseInt(limit));

    // Fetch current data for popular stocks with real-time prices
    const stocksData = await Promise.allSettled(
      popularStocks.map(async (symbol) => {
        try {
          const data = await stockService.getCurrentPrice(symbol, 'NSE');
          return {
            ...data,
            isPopular: true,
            rank: popularStocks.indexOf(symbol) + 1
          };
        } catch (error) {
          console.error(`Error fetching ${symbol}:`, error.message);
          // Return basic info if real-time data fails
          return {
            symbol,
            name: `${symbol} Limited`,
            exchange: 'NSE',
            currentPrice: 0,
            dayChange: 0,
            dayChangePercent: 0,
            error: 'Data unavailable',
            isPopular: true,
            rank: popularStocks.indexOf(symbol) + 1
          };
        }
      })
    );

    const successfulData = stocksData
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value)
      .filter(stock => stock.currentPrice > 0); // Only include stocks with valid prices

    res.json({
      success: true,
      data: successfulData,
      count: successfulData.length,
      message: `Fetched ${successfulData.length} popular stocks with real-time data`
    });

  } catch (error) {
    console.error('Popular stocks fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch popular stocks',
      error: error.message
    });
  }
});

module.exports = router;
