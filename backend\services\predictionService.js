const axios = require('axios');
const Prediction = require('../models/Prediction');
const stockService = require('./stockService');

class PredictionService {
  constructor() {
    this.mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:8080';
    this.mcpApiKey = process.env.MCP_API_KEY;
  }

  // Generate stock prediction using MCP
  async generatePrediction(symbol, exchange = 'NSE', timeframe = '1d', predictionType = 'short_term', userId = null) {
    try {
      // Get current stock data and historical data
      const currentData = await stockService.getCurrentPrice(symbol, exchange);
      const historicalData = await stockService.getHistoricalData(symbol, exchange, '1y');
      const technicalIndicators = stockService.calculateTechnicalIndicators(historicalData);

      // Prepare data for MCP model
      const mcpInput = {
        symbol: symbol.toUpperCase(),
        exchange,
        currentPrice: currentData.currentPrice,
        historicalData: historicalData.slice(-60), // Last 60 days
        technicalIndicators,
        marketData: {
          volume: currentData.volume,
          marketCap: currentData.marketCap,
          dayChange: currentData.dayChange,
          dayChangePercent: currentData.dayChangePercent
        },
        timeframe,
        predictionType
      };

      // Call MCP service for prediction
      const mcpResponse = await this.callMCPService(mcpInput);

      // Calculate target date based on timeframe
      const targetDate = this.calculateTargetDate(timeframe);

      // Create prediction object
      const prediction = new Prediction({
        userId,
        stockSymbol: symbol.toUpperCase(),
        exchange,
        predictionType,
        timeframe,
        currentPrice: currentData.currentPrice,
        predictedPrice: mcpResponse.predictedPrice,
        confidence: mcpResponse.confidence,
        direction: mcpResponse.direction,
        expectedReturn: mcpResponse.predictedPrice - currentData.currentPrice,
        expectedReturnPercent: ((mcpResponse.predictedPrice - currentData.currentPrice) / currentData.currentPrice) * 100,
        riskLevel: mcpResponse.riskLevel,
        technicalIndicators,
        marketSentiment: mcpResponse.marketSentiment,
        newsImpact: mcpResponse.newsImpact,
        modelUsed: 'MCP_AI_Model',
        modelVersion: mcpResponse.modelVersion || '1.0',
        accuracy: mcpResponse.expectedAccuracy,
        targetDate,
        predictionDate: new Date()
      });

      await prediction.save();
      return prediction;

    } catch (error) {
      console.error(`Error generating prediction for ${symbol}:`, error.message);
      throw new Error(`Failed to generate prediction: ${error.message}`);
    }
  }

  // Call MCP service for AI prediction
  async callMCPService(inputData) {
    try {
      // If MCP service is not available, use fallback prediction logic
      if (!this.mcpServerUrl || !this.mcpApiKey) {
        return this.fallbackPrediction(inputData);
      }

      const response = await axios.post(`${this.mcpServerUrl}/predict`, inputData, {
        headers: {
          'Authorization': `Bearer ${this.mcpApiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30 seconds timeout
      });

      return response.data;
    } catch (error) {
      console.warn('MCP service unavailable, using fallback prediction:', error.message);
      return this.fallbackPrediction(inputData);
    }
  }

  // Fallback prediction logic when MCP is not available
  fallbackPrediction(inputData) {
    const { currentPrice, technicalIndicators, timeframe, historicalData } = inputData;

    // Simple prediction based on technical indicators and trends
    let predictedPrice = currentPrice;
    let confidence = 60; // Base confidence
    let direction = 'neutral';
    let riskLevel = 'medium';
    let marketSentiment = 'neutral';

    if (technicalIndicators) {
      // RSI-based prediction
      if (technicalIndicators.rsi < 30) {
        // Oversold - potential upward movement
        predictedPrice *= 1.02; // 2% increase
        direction = 'bullish';
        confidence += 10;
      } else if (technicalIndicators.rsi > 70) {
        // Overbought - potential downward movement
        predictedPrice *= 0.98; // 2% decrease
        direction = 'bearish';
        confidence += 10;
      }

      // Moving average crossover
      if (technicalIndicators.sma20 > technicalIndicators.sma50) {
        predictedPrice *= 1.01; // Additional 1% increase
        confidence += 5;
        if (direction === 'neutral') direction = 'bullish';
      } else if (technicalIndicators.sma20 < technicalIndicators.sma50) {
        predictedPrice *= 0.99; // Additional 1% decrease
        confidence += 5;
        if (direction === 'neutral') direction = 'bearish';
      }

      // Bollinger Bands
      if (currentPrice <= technicalIndicators.bollinger.lower) {
        predictedPrice *= 1.015; // 1.5% increase
        confidence += 8;
      } else if (currentPrice >= technicalIndicators.bollinger.upper) {
        predictedPrice *= 0.985; // 1.5% decrease
        confidence += 8;
      }
    }

    // Adjust based on timeframe
    const timeframeMultipliers = {
      '1d': 1.0,
      '1w': 1.5,
      '1m': 2.0,
      '3m': 3.0,
      '6m': 4.0,
      '1y': 6.0
    };

    const multiplier = timeframeMultipliers[timeframe] || 1.0;
    const priceChange = predictedPrice - currentPrice;
    predictedPrice = currentPrice + (priceChange * multiplier);

    // Determine risk level based on volatility
    if (historicalData && historicalData.length > 20) {
      const recentPrices = historicalData.slice(-20).map(d => d.close);
      const volatility = this.calculateVolatility(recentPrices);

      if (volatility < 0.02) {
        riskLevel = 'low';
      } else if (volatility > 0.05) {
        riskLevel = 'high';
        confidence -= 10;
      }
    }

    // Market sentiment based on recent trend
    if (historicalData && historicalData.length > 5) {
      const recentTrend = this.calculateTrend(historicalData.slice(-5));
      if (recentTrend > 0.02) {
        marketSentiment = 'positive';
      } else if (recentTrend < -0.02) {
        marketSentiment = 'negative';
      }
    }

    // Ensure confidence is within bounds
    confidence = Math.max(30, Math.min(95, confidence));

    return {
      predictedPrice: Math.round(predictedPrice * 100) / 100,
      confidence,
      direction,
      riskLevel,
      marketSentiment,
      newsImpact: 'neutral',
      expectedAccuracy: confidence,
      modelVersion: '1.0-fallback'
    };
  }

  // Calculate volatility
  calculateVolatility(prices) {
    if (prices.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }

    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  // Calculate trend
  calculateTrend(data) {
    if (data.length < 2) return 0;

    const firstPrice = data[0].close;
    const lastPrice = data[data.length - 1].close;

    return (lastPrice - firstPrice) / firstPrice;
  }

  // Calculate target date based on timeframe
  calculateTargetDate(timeframe) {
    const now = new Date();
    const timeframeMap = {
      '1d': 1,
      '1w': 7,
      '1m': 30,
      '3m': 90,
      '6m': 180,
      '1y': 365
    };

    const days = timeframeMap[timeframe] || 1;
    return new Date(now.getTime() + (days * 24 * 60 * 60 * 1000));
  }

  // Get predictions for a stock
  async getPredictions(symbol, exchange = 'NSE', limit = 10) {
    try {
      return await Prediction.find({
        stockSymbol: symbol.toUpperCase(),
        exchange,
        isActive: true
      })
        .sort({ predictionDate: -1 })
        .limit(limit);
    } catch (error) {
      console.error(`Error fetching predictions for ${symbol}:`, error.message);
      throw new Error('Failed to fetch predictions');
    }
  }

  // Update prediction with actual results
  async updatePredictionResult(predictionId, actualPrice) {
    try {
      const prediction = await Prediction.findById(predictionId);
      if (!prediction) {
        throw new Error('Prediction not found');
      }

      prediction.actualPrice = actualPrice;
      prediction.calculateAccuracy();
      await prediction.save();

      return prediction;
    } catch (error) {
      console.error('Error updating prediction result:', error.message);
      throw error;
    }
  }

  // Get prediction statistics
  async getPredictionStats(symbol, exchange = 'NSE') {
    try {
      const stats = await Prediction.getStats(symbol, exchange);
      return stats[0] || {
        avgAccuracy: 0,
        totalPredictions: 0,
        successfulPredictions: 0
      };
    } catch (error) {
      console.error('Error fetching prediction stats:', error.message);
      throw new Error('Failed to fetch prediction statistics');
    }
  }

  // Batch generate predictions for multiple stocks
  async batchGeneratePredictions(stocks, timeframe = '1d', userId = null) {
    const results = [];

    for (const stock of stocks) {
      try {
        const prediction = await this.generatePrediction(
          stock.symbol,
          stock.exchange || 'NSE',
          timeframe,
          'short_term',
          userId
        );
        results.push({ success: true, prediction });
      } catch (error) {
        results.push({
          success: false,
          symbol: stock.symbol,
          error: error.message
        });
      }
    }

    return results;
  }
}

module.exports = new PredictionService();
