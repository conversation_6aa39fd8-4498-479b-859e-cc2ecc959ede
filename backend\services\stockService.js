const axios = require('axios');
const cheerio = require('cheerio');
const yahooFinance = require('yahoo-finance2').default;
const Stock = require('../models/Stock');

class StockService {
  constructor() {
    this.nseBaseUrl = 'https://www.nseindia.com/api';
    this.bseBaseUrl = 'https://api.bseindia.com';
    this.alphaVantageKey = process.env.ALPHA_VANTAGE_API_KEY;
    this.stockCache = new Map(); // Cache for stock lists
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours
  }

  // Get Indian stock symbols with .NS (NSE) or .BO (BSE) suffix for Yahoo Finance
  getYahooSymbol(symbol, exchange = 'NSE') {
    const suffix = exchange === 'NSE' ? '.NS' : '.BO';
    return `${symbol}${suffix}`;
  }

  // Fetch current stock price and basic info
  async getCurrentPrice(symbol, exchange = 'NSE') {
    try {
      const yahooSymbol = this.getYahooSymbol(symbol, exchange);
      const quote = await yahooFinance.quote(yahooSymbol);

      return {
        symbol: symbol.toUpperCase(),
        exchange,
        currentPrice: quote.regularMarketPrice,
        dayChange: quote.regularMarketChange,
        dayChangePercent: quote.regularMarketChangePercent,
        volume: quote.regularMarketVolume,
        marketCap: quote.marketCap,
        name: quote.longName || quote.shortName,
        currency: quote.currency,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error(`Error fetching current price for ${symbol}:`, error.message);
      throw new Error(`Failed to fetch current price for ${symbol}`);
    }
  }

  // Fetch historical data
  async getHistoricalData(symbol, exchange = 'NSE', period = '1y') {
    try {
      const yahooSymbol = this.getYahooSymbol(symbol, exchange);

      // Convert period to Yahoo Finance format
      const periodMap = {
        '1d': '1d',
        '5d': '5d',
        '1m': '1mo',
        '3m': '3mo',
        '6m': '6mo',
        '1y': '1y',
        '2y': '2y',
        '5y': '5y',
        '10y': '10y'
      };

      const yahooHistory = await yahooFinance.historical(yahooSymbol, {
        period1: this.getPeriodStartDate(period),
        period2: new Date(),
        interval: '1d'
      });

      return yahooHistory.map(item => ({
        date: item.date,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        adjustedClose: item.adjClose
      }));
    } catch (error) {
      console.error(`Error fetching historical data for ${symbol}:`, error.message);
      throw new Error(`Failed to fetch historical data for ${symbol}`);
    }
  }

  // Get period start date
  getPeriodStartDate(period) {
    const now = new Date();
    const periodMap = {
      '1d': 1,
      '5d': 5,
      '1m': 30,
      '3m': 90,
      '6m': 180,
      '1y': 365,
      '2y': 730,
      '5y': 1825,
      '10y': 3650
    };

    const days = periodMap[period] || 365;
    return new Date(now.getTime() - (days * 24 * 60 * 60 * 1000));
  }

  // Fetch NIFTY 50 stocks dynamically
  async getNifty50Stocks() {
    const cacheKey = 'nifty50';
    const cached = this.stockCache.get(cacheKey);

    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      return cached.data;
    }

    try {
      const url = process.env.NIFTY_50_URL;
      const response = await axios.get(url);
      const $ = cheerio.load(response.data);

      const nifty50Stocks = [];

      $('table#constituents').eq(0).find('tbody tr').each((index, element) => {
        const tds = $(element).find('td');
        if (tds.length >= 3) {

          const company = tds.eq(0).text().trim();
          const symbol = tds.eq(1).text().trim();
          const sector = tds.eq(2).text().trim();
          if (company && symbol && sector) {
            nifty50Stocks.push({ company, symbol, sector });
          }
        }
      });

      this.stockCache.set(cacheKey, {
        data: nifty50Stocks,
        timestamp: Date.now()
      });
      return nifty50Stocks;
    } catch (error) {
      console.error('Error fetching NIFTY 50 stocks:', error.message);
      throw new Error('Failed to fetch NIFTY 50 stocks');
    }
  }

  // Search for stocks using Yahoo Finance search
  async searchStocksWithYahoo(query) {
    try {
      // Use Yahoo Finance search functionality
      const searchResults = await yahooFinance.search(query, {
        quotesCount: 10,
        newsCount: 0
      });

      if (searchResults && searchResults.quotes) {
        // Filter for Indian stocks (NSE/BSE)
        const indianStocks = searchResults.quotes.filter(stock =>
          stock.symbol && (
            stock.symbol.endsWith('.NS') ||
            stock.symbol.endsWith('.BO') ||
            stock.exchange === 'NSI' ||
            stock.exchange === 'BSE'
          )
        );

        return indianStocks.map(stock => ({
          symbol: stock.symbol.replace(/\.(NS|BO)$/, ''),
          name: stock.longname || stock.shortname || stock.symbol,
          exchange: stock.symbol.endsWith('.NS') ? 'NSE' : 'BSE',
          sector: stock.sector || 'Unknown',
          yahooSymbol: stock.symbol
        }));
      }

      return [];
    } catch (error) {
      console.error('Error searching with Yahoo Finance:', error.message);
      return [];
    }
  }

  // Search for stocks
  async searchStocks(query, exchange = 'NSE') {
    try {
      // First try Yahoo Finance search for broader results
      let searchResults = await this.searchStocksWithYahoo(query);

      // Filter by exchange if specified
      if (exchange) {
        searchResults = searchResults.filter(stock => stock.exchange === exchange);
      }

      // If Yahoo search doesn't return enough results, fallback to NIFTY 50 search
      if (!searchResults?.length) {
        const nifty50Stocks = await this.getNifty50Stocks();
        const niftyFiltered = nifty50Stocks.filter(stock =>
          stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
          stock.name.toLowerCase().includes(query.toLowerCase()) ||
          stock.sector.toLowerCase().includes(query.toLowerCase())
        );

        // Add exchange info and merge with Yahoo results
        const niftyWithExchange = niftyFiltered.map(stock => ({
          ...stock,
          exchange: exchange
        }));

        // Combine and deduplicate results
        const combined = [...searchResults, ...niftyWithExchange];
        const uniqueResults = combined.filter((stock, index, self) =>
          index === self.findIndex(s => s.symbol === stock.symbol)
        );

        searchResults = uniqueResults;
      }

      // Get real-time data for search results
      const stocksWithData = await Promise.allSettled(
        searchResults.slice(0, 10).map(async (stock) => {
          try {
            const realTimeData = await this.getCurrentPrice(stock.symbol, stock.exchange);
            return {
              ...stock,
              currentPrice: realTimeData.currentPrice,
              dayChange: realTimeData.dayChange,
              dayChangePercent: realTimeData.dayChangePercent,
              volume: realTimeData.volume,
              marketCap: realTimeData.marketCap,
              lastUpdated: realTimeData.lastUpdated
            };
          } catch (error) {
            // Return basic info if real-time data fails
            return {
              ...stock,
              currentPrice: null,
              dayChange: null,
              dayChangePercent: null,
              error: 'Real-time data unavailable'
            };
          }
        })
      );
      console.log(stocksWithData);
      const results = stocksWithData
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);

      return results;
    } catch (error) {
      console.error('Error searching stocks:', error.message);
      throw new Error('Failed to search stocks');
    }
  }

  // Update stock data in database
  async updateStockData(symbol, exchange = 'NSE') {
    try {
      const currentData = await this.getCurrentPrice(symbol, exchange);
      const historicalData = await this.getHistoricalData(symbol, exchange, '1y');

      let stock = await Stock.findBySymbol(symbol, exchange);

      if (!stock) {
        stock = new Stock({
          symbol: symbol.toUpperCase(),
          name: currentData.name,
          exchange
        });
      }

      // Update current data
      stock.currentPrice = currentData.currentPrice;
      stock.dayChange = currentData.dayChange;
      stock.dayChangePercent = currentData.dayChangePercent;
      stock.marketCap = currentData.marketCap;
      stock.lastUpdated = new Date();

      // Add historical data
      historicalData.forEach(data => {
        stock.addHistoricalData(data);
      });

      await stock.save();
      return stock;
    } catch (error) {
      console.error(`Error updating stock data for ${symbol}:`, error.message);
      throw error;
    }
  }

  // Get top gainers/losers
  async getTopMovers(type = 'gainers', limit = 10) {
    try {
      // Get NIFTY 50 stocks dynamically for analysis
      const nifty50Stocks = await this.getNifty50Stocks();
      const stocksToAnalyze = nifty50Stocks.map(stock => stock.symbol);

      // Fetch current data for all stocks
      const stocksData = await Promise.allSettled(
        stocksToAnalyze.map(async (symbol) => {
          try {
            const data = await this.getCurrentPrice(symbol, 'NSE');
            return {
              symbol: data.symbol,
              name: data.name,
              exchange: data.exchange,
              currentPrice: data.currentPrice,
              dayChange: data.dayChange,
              dayChangePercent: data.dayChangePercent,
              volume: data.volume,
              marketCap: data.marketCap,
              sector: nifty50Stocks.find(s => s.symbol === symbol)?.sector || 'Unknown'
            };
          } catch (error) {
            return null;
          }
        })
      );

      // Filter successful results and remove nulls
      const validStocks = stocksData
        .filter(result => result.status === 'fulfilled' && result.value !== null)
        .map(result => result.value)
        .filter(stock => stock.dayChangePercent !== undefined && stock.dayChangePercent !== null);

      // Sort based on type
      let sortedStocks;
      if (type === 'gainers') {
        sortedStocks = validStocks
          .filter(stock => stock.dayChangePercent > 0)
          .sort((a, b) => b.dayChangePercent - a.dayChangePercent);
      } else {
        sortedStocks = validStocks
          .filter(stock => stock.dayChangePercent < 0)
          .sort((a, b) => a.dayChangePercent - b.dayChangePercent);
      }

      return sortedStocks.slice(0, parseInt(limit));
    } catch (error) {
      console.error('Error fetching top movers:', error.message);
      throw new Error('Failed to fetch top movers');
    }
  }

  // Calculate technical indicators
  calculateTechnicalIndicators(historicalData) {
    if (!historicalData || historicalData.length < 20) {
      return null;
    }

    const closes = historicalData.map(d => d.close);
    const highs = historicalData.map(d => d.high);
    const lows = historicalData.map(d => d.low);

    // Simple Moving Averages
    const sma20 = this.calculateSMA(closes, 20);
    const sma50 = this.calculateSMA(closes, 50);

    // RSI
    const rsi = this.calculateRSI(closes, 14);

    // Bollinger Bands
    const bollinger = this.calculateBollingerBands(closes, 20, 2);

    // Support and Resistance
    const support = Math.min(...lows.slice(-20));
    const resistance = Math.max(...highs.slice(-20));

    return {
      sma20: sma20[sma20.length - 1],
      sma50: sma50[sma50.length - 1],
      rsi: rsi[rsi.length - 1],
      bollinger: {
        upper: bollinger.upper[bollinger.upper.length - 1],
        middle: bollinger.middle[bollinger.middle.length - 1],
        lower: bollinger.lower[bollinger.lower.length - 1]
      },
      support,
      resistance
    };
  }

  // Helper methods for technical indicators
  calculateSMA(data, period) {
    const result = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    return result;
  }

  calculateRSI(data, period) {
    const gains = [];
    const losses = [];

    for (let i = 1; i < data.length; i++) {
      const change = data[i] - data[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    const result = [];
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;

      if (avgLoss === 0) {
        result.push(100);
      } else {
        const rs = avgGain / avgLoss;
        result.push(100 - (100 / (1 + rs)));
      }
    }

    return result;
  }

  calculateBollingerBands(data, period, multiplier) {
    const sma = this.calculateSMA(data, period);
    const upper = [];
    const lower = [];

    for (let i = period - 1; i < data.length; i++) {
      const slice = data.slice(i - period + 1, i + 1);
      const mean = sma[i - period + 1];
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
      const stdDev = Math.sqrt(variance);

      upper.push(mean + (multiplier * stdDev));
      lower.push(mean - (multiplier * stdDev));
    }

    return { upper, middle: sma, lower };
  }
}

module.exports = new StockService();
