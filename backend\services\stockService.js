const axios = require('axios');
const yahooFinance = require('yahoo-finance2').default;
const Stock = require('../models/Stock');

class StockService {
  constructor() {
    this.nseBaseUrl = 'https://www.nseindia.com/api';
    this.bseBaseUrl = 'https://api.bseindia.com';
    this.alphaVantageKey = process.env.ALPHA_VANTAGE_API_KEY;
  }

  // Get Indian stock symbols with .NS (NSE) or .BO (BSE) suffix for Yahoo Finance
  getYahooSymbol(symbol, exchange = 'NSE') {
    const suffix = exchange === 'NSE' ? '.NS' : '.BO';
    return `${symbol}${suffix}`;
  }

  // Fetch current stock price and basic info
  async getCurrentPrice(symbol, exchange = 'NSE') {
    try {
      const yahooSymbol = this.getYahooSymbol(symbol, exchange);
      const quote = await yahooFinance.quote(yahooSymbol);

      return {
        symbol: symbol.toUpperCase(),
        exchange,
        currentPrice: quote.regularMarketPrice,
        dayChange: quote.regularMarketChange,
        dayChangePercent: quote.regularMarketChangePercent,
        volume: quote.regularMarketVolume,
        marketCap: quote.marketCap,
        name: quote.longName || quote.shortName,
        currency: quote.currency,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error(`Error fetching current price for ${symbol}:`, error.message);
      throw new Error(`Failed to fetch current price for ${symbol}`);
    }
  }

  // Fetch historical data
  async getHistoricalData(symbol, exchange = 'NSE', period = '1y') {
    try {
      const yahooSymbol = this.getYahooSymbol(symbol, exchange);

      // Convert period to Yahoo Finance format
      const periodMap = {
        '1d': '1d',
        '5d': '5d',
        '1m': '1mo',
        '3m': '3mo',
        '6m': '6mo',
        '1y': '1y',
        '2y': '2y',
        '5y': '5y',
        '10y': '10y'
      };

      const yahooHistory = await yahooFinance.historical(yahooSymbol, {
        period1: this.getPeriodStartDate(period),
        period2: new Date(),
        interval: '1d'
      });

      return yahooHistory.map(item => ({
        date: item.date,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        adjustedClose: item.adjClose
      }));
    } catch (error) {
      console.error(`Error fetching historical data for ${symbol}:`, error.message);
      throw new Error(`Failed to fetch historical data for ${symbol}`);
    }
  }

  // Get period start date
  getPeriodStartDate(period) {
    const now = new Date();
    const periodMap = {
      '1d': 1,
      '5d': 5,
      '1m': 30,
      '3m': 90,
      '6m': 180,
      '1y': 365,
      '2y': 730,
      '5y': 1825,
      '10y': 3650
    };

    const days = periodMap[period] || 365;
    return new Date(now.getTime() - (days * 24 * 60 * 60 * 1000));
  }

  // Search for stocks
  async searchStocks(query, exchange = 'NSE') {
    try {
      // Comprehensive list of Indian stocks for search
      const stockDatabase = [
        { symbol: 'RELIANCE', name: 'Reliance Industries Limited', exchange: 'NSE', sector: 'Oil & Gas' },
        { symbol: 'TCS', name: 'Tata Consultancy Services Limited', exchange: 'NSE', sector: 'IT Services' },
        { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', exchange: 'NSE', sector: 'Banking' },
        { symbol: 'INFY', name: 'Infosys Limited', exchange: 'NSE', sector: 'IT Services' },
        { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', exchange: 'NSE', sector: 'FMCG' },
        { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', exchange: 'NSE', sector: 'Banking' },
        { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Limited', exchange: 'NSE', sector: 'Banking' },
        { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', exchange: 'NSE', sector: 'Telecom' },
        { symbol: 'ITC', name: 'ITC Limited', exchange: 'NSE', sector: 'FMCG' },
        { symbol: 'SBIN', name: 'State Bank of India', exchange: 'NSE', sector: 'Banking' },
        { symbol: 'LT', name: 'Larsen & Toubro Limited', exchange: 'NSE', sector: 'Engineering' },
        { symbol: 'ASIANPAINT', name: 'Asian Paints Limited', exchange: 'NSE', sector: 'Paints' },
        { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', exchange: 'NSE', sector: 'Automobile' },
        { symbol: 'TITAN', name: 'Titan Company Limited', exchange: 'NSE', sector: 'Jewellery' },
        { symbol: 'WIPRO', name: 'Wipro Limited', exchange: 'NSE', sector: 'IT Services' },
        { symbol: 'NESTLEIND', name: 'Nestle India Limited', exchange: 'NSE', sector: 'FMCG' },
        { symbol: 'HCLTECH', name: 'HCL Technologies Limited', exchange: 'NSE', sector: 'IT Services' },
        { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', exchange: 'NSE', sector: 'NBFC' },
        { symbol: 'POWERGRID', name: 'Power Grid Corporation of India Limited', exchange: 'NSE', sector: 'Power' },
        { symbol: 'NTPC', name: 'NTPC Limited', exchange: 'NSE', sector: 'Power' },
        { symbol: 'AXISBANK', name: 'Axis Bank Limited', exchange: 'NSE', sector: 'Banking' },
        { symbol: 'ULTRACEMCO', name: 'UltraTech Cement Limited', exchange: 'NSE', sector: 'Cement' },
        { symbol: 'ONGC', name: 'Oil and Natural Gas Corporation Limited', exchange: 'NSE', sector: 'Oil & Gas' },
        { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical Industries Limited', exchange: 'NSE', sector: 'Pharma' },
        { symbol: 'TECHM', name: 'Tech Mahindra Limited', exchange: 'NSE', sector: 'IT Services' },
        { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', exchange: 'NSE', sector: 'Automobile' },
        { symbol: 'COALINDIA', name: 'Coal India Limited', exchange: 'NSE', sector: 'Mining' },
        { symbol: 'INDUSINDBK', name: 'IndusInd Bank Limited', exchange: 'NSE', sector: 'Banking' },
        { symbol: 'ADANIPORTS', name: 'Adani Ports and Special Economic Zone Limited', exchange: 'NSE', sector: 'Infrastructure' },
        { symbol: 'JSWSTEEL', name: 'JSW Steel Limited', exchange: 'NSE', sector: 'Steel' }
      ];

      // Filter stocks based on query
      const filtered = stockDatabase.filter(stock =>
        stock.exchange === exchange && (
          stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
          stock.name.toLowerCase().includes(query.toLowerCase()) ||
          stock.sector.toLowerCase().includes(query.toLowerCase())
        )
      );

      // Get real-time data for filtered stocks
      const stocksWithData = await Promise.allSettled(
        filtered.slice(0, 10).map(async (stock) => {
          try {
            const realTimeData = await this.getCurrentPrice(stock.symbol, stock.exchange);
            return {
              ...stock,
              currentPrice: realTimeData.currentPrice,
              dayChange: realTimeData.dayChange,
              dayChangePercent: realTimeData.dayChangePercent,
              volume: realTimeData.volume,
              marketCap: realTimeData.marketCap,
              lastUpdated: realTimeData.lastUpdated
            };
          } catch (error) {
            // Return basic info if real-time data fails
            return {
              ...stock,
              currentPrice: null,
              dayChange: null,
              dayChangePercent: null,
              error: 'Real-time data unavailable'
            };
          }
        })
      );

      const results = stocksWithData
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);

      return results;
    } catch (error) {
      console.error('Error searching stocks:', error.message);
      throw new Error('Failed to search stocks');
    }
  }

  // Update stock data in database
  async updateStockData(symbol, exchange = 'NSE') {
    try {
      const currentData = await this.getCurrentPrice(symbol, exchange);
      const historicalData = await this.getHistoricalData(symbol, exchange, '1y');

      let stock = await Stock.findBySymbol(symbol, exchange);

      if (!stock) {
        stock = new Stock({
          symbol: symbol.toUpperCase(),
          name: currentData.name,
          exchange
        });
      }

      // Update current data
      stock.currentPrice = currentData.currentPrice;
      stock.dayChange = currentData.dayChange;
      stock.dayChangePercent = currentData.dayChangePercent;
      stock.marketCap = currentData.marketCap;
      stock.lastUpdated = new Date();

      // Add historical data
      historicalData.forEach(data => {
        stock.addHistoricalData(data);
      });

      await stock.save();
      return stock;
    } catch (error) {
      console.error(`Error updating stock data for ${symbol}:`, error.message);
      throw error;
    }
  }

  // Get top gainers/losers
  async getTopMovers(type = 'gainers', limit = 10) {
    try {
      // Get a broader list of stocks to analyze for top movers
      const stocksToAnalyze = [
        'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR',
        'ICICIBANK', 'KOTAKBANK', 'BHARTIARTL', 'ITC', 'SBIN',
        'LT', 'ASIANPAINT', 'MARUTI', 'TITAN', 'WIPRO',
        'NESTLEIND', 'HCLTECH', 'BAJFINANCE', 'POWERGRID', 'NTPC',
        'AXISBANK', 'ULTRACEMCO', 'ONGC', 'SUNPHARMA', 'TECHM',
        'TATAMOTORS', 'COALINDIA', 'INDUSINDBK', 'ADANIPORTS', 'JSWSTEEL',
        'DRREDDY', 'EICHERMOT', 'HEROMOTOCO', 'CIPLA', 'GRASIM',
        'BRITANNIA', 'DIVISLAB', 'TATACONSUM', 'APOLLOHOSP', 'BAJAJFINSV'
      ];

      // Fetch current data for all stocks
      const stocksData = await Promise.allSettled(
        stocksToAnalyze.map(async (symbol) => {
          try {
            const data = await this.getCurrentPrice(symbol, 'NSE');
            return {
              symbol: data.symbol,
              name: data.name,
              exchange: data.exchange,
              currentPrice: data.currentPrice,
              dayChange: data.dayChange,
              dayChangePercent: data.dayChangePercent,
              volume: data.volume,
              marketCap: data.marketCap
            };
          } catch (error) {
            return null;
          }
        })
      );

      // Filter successful results and remove nulls
      const validStocks = stocksData
        .filter(result => result.status === 'fulfilled' && result.value !== null)
        .map(result => result.value)
        .filter(stock => stock.dayChangePercent !== undefined && stock.dayChangePercent !== null);

      // Sort based on type
      let sortedStocks;
      if (type === 'gainers') {
        sortedStocks = validStocks
          .filter(stock => stock.dayChangePercent > 0)
          .sort((a, b) => b.dayChangePercent - a.dayChangePercent);
      } else {
        sortedStocks = validStocks
          .filter(stock => stock.dayChangePercent < 0)
          .sort((a, b) => a.dayChangePercent - b.dayChangePercent);
      }

      return sortedStocks.slice(0, parseInt(limit));
    } catch (error) {
      console.error('Error fetching top movers:', error.message);
      throw new Error('Failed to fetch top movers');
    }
  }

  // Calculate technical indicators
  calculateTechnicalIndicators(historicalData) {
    if (!historicalData || historicalData.length < 20) {
      return null;
    }

    const closes = historicalData.map(d => d.close);
    const highs = historicalData.map(d => d.high);
    const lows = historicalData.map(d => d.low);

    // Simple Moving Averages
    const sma20 = this.calculateSMA(closes, 20);
    const sma50 = this.calculateSMA(closes, 50);

    // RSI
    const rsi = this.calculateRSI(closes, 14);

    // Bollinger Bands
    const bollinger = this.calculateBollingerBands(closes, 20, 2);

    // Support and Resistance
    const support = Math.min(...lows.slice(-20));
    const resistance = Math.max(...highs.slice(-20));

    return {
      sma20: sma20[sma20.length - 1],
      sma50: sma50[sma50.length - 1],
      rsi: rsi[rsi.length - 1],
      bollinger: {
        upper: bollinger.upper[bollinger.upper.length - 1],
        middle: bollinger.middle[bollinger.middle.length - 1],
        lower: bollinger.lower[bollinger.lower.length - 1]
      },
      support,
      resistance
    };
  }

  // Helper methods for technical indicators
  calculateSMA(data, period) {
    const result = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    return result;
  }

  calculateRSI(data, period) {
    const gains = [];
    const losses = [];

    for (let i = 1; i < data.length; i++) {
      const change = data[i] - data[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    const result = [];
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;

      if (avgLoss === 0) {
        result.push(100);
      } else {
        const rs = avgGain / avgLoss;
        result.push(100 - (100 / (1 + rs)));
      }
    }

    return result;
  }

  calculateBollingerBands(data, period, multiplier) {
    const sma = this.calculateSMA(data, period);
    const upper = [];
    const lower = [];

    for (let i = period - 1; i < data.length; i++) {
      const slice = data.slice(i - period + 1, i + 1);
      const mean = sma[i - period + 1];
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
      const stdDev = Math.sqrt(variance);

      upper.push(mean + (multiplier * stdDev));
      lower.push(mean - (multiplier * stdDev));
    }

    return { upper, middle: sma, lower };
  }
}

module.exports = new StockService();
