{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, DOCUMENT, Injector, afterNextRender, ViewChild, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { OverlayConfig, createGlobalPositionStrategy, createOverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport './icon-button-DxiIc1ex.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-BnMiRtmT.mjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-CObeNzjn.mjs';\nimport './index-BFRo2fUq.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_Conditional_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  _overlayRef;\n  /** The instance of the component making up the content of the snack bar. */\n  instance;\n  /**\n   * The instance of the component making up the content of the snack bar.\n   * @docs-private\n   */\n  containerInstance;\n  /** Subject for notifying the user that the snack bar has been dismissed. */\n  _afterDismissed = new Subject();\n  /** Subject for notifying the user that the snack bar has opened and appeared. */\n  _afterOpened = new Subject();\n  /** Subject for notifying the user that the snack bar action was called. */\n  _onAction = new Subject();\n  /**\n   * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n   * dismissed before the duration passes.\n   */\n  _durationTimeoutId;\n  /** Whether the snack bar was dismissed using the action button. */\n  _dismissedByAction = false;\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n  politeness = 'polite';\n  /**\n   * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n   * component or template, the announcement message will default to the specified message.\n   */\n  announcementMessage = '';\n  /**\n   * The view container that serves as the parent for the snackbar for the purposes of dependency\n   * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n   */\n  viewContainerRef;\n  /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n  duration = 0;\n  /** Extra CSS classes to be added to the snack bar container. */\n  panelClass;\n  /** Text layout direction for the snack bar. */\n  direction;\n  /** Data being injected into the child component. */\n  data = null;\n  /** The horizontal position to place the snack bar. */\n  horizontalPosition = 'center';\n  /** The vertical position to place the snack bar. */\n  verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n  static ɵfac = function MatSnackBarLabel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarLabel)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarLabel,\n    selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n  static ɵfac = function MatSnackBarActions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarActions)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarActions,\n    selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n  static ɵfac = function MatSnackBarAction_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarAction)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarAction,\n    selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\nclass SimpleSnackBar {\n  snackBarRef = inject(MatSnackBarRef);\n  data = inject(MAT_SNACK_BAR_DATA);\n  constructor() {}\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n  static ɵfac = function SimpleSnackBar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SimpleSnackBar)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: SimpleSnackBar,\n    selectors: [[\"simple-snack-bar\"]],\n    hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n    exportAs: [\"matSnackBar\"],\n    decls: 3,\n    vars: 2,\n    consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\"], [\"matButton\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n    template: function SimpleSnackBar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtext(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵconditionalCreate(2, SimpleSnackBar_Conditional_2_Template, 3, 1, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.hasAction ? 2 : -1);\n      }\n    },\n    dependencies: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n    styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button matButton matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"]\n    }]\n  }], () => [], null);\n})();\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n  _ngZone = inject(NgZone);\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _platform = inject(Platform);\n  _animationsDisabled = _animationsDisabled();\n  snackBarConfig = inject(MatSnackBarConfig);\n  _document = inject(DOCUMENT);\n  _trackedModals = new Set();\n  _enterFallback;\n  _exitFallback;\n  _injector = inject(Injector);\n  /** The number of milliseconds to wait before announcing the snack bar's content. */\n  _announceDelay = 150;\n  /** The timeout for announcing the snack bar's content. */\n  _announceTimeoutId;\n  /** Whether the component has been destroyed. */\n  _destroyed = false;\n  /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n  _portalOutlet;\n  /** Subject for notifying that the snack bar has announced to screen readers. */\n  _onAnnounce = new Subject();\n  /** Subject for notifying that the snack bar has exited from view. */\n  _onExit = new Subject();\n  /** Subject for notifying that the snack bar has finished entering the view. */\n  _onEnter = new Subject();\n  /** The state of the snack bar animations. */\n  _animationState = 'void';\n  /** aria-live value for the live region. */\n  _live;\n  /**\n   * Element that will have the `mdc-snackbar__label` class applied if the attached component\n   * or template does not have it. This ensures that the appropriate structure, typography, and\n   * color is applied to the attached view.\n   */\n  _label;\n  /**\n   * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n   * JAWS does not read out aria-live message.\n   */\n  _role;\n  /** Unique ID of the aria-live element. */\n  _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n  constructor() {\n    super();\n    const config = this.snackBarConfig;\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (config.politeness === 'assertive' && !config.announcementMessage) {\n      this._live = 'assertive';\n    } else if (config.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /**\n   * Attaches a DOM portal to the snack bar container.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  };\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(animationName) {\n    if (animationName === EXIT_ANIMATION) {\n      this._completeExit();\n    } else if (animationName === ENTER_ANIMATION) {\n      clearTimeout(this._enterFallback);\n      this._ngZone.run(() => {\n        this._onEnter.next();\n        this._onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n      // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n      this._changeDetectorRef.markForCheck();\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n      if (this._animationsDisabled) {\n        afterNextRender(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n        }, {\n          injector: this._injector\n        });\n      } else {\n        clearTimeout(this._enterFallback);\n        this._enterFallback = setTimeout(() => {\n          // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n          // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n          this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n          this.onAnimationEnd(ENTER_ANIMATION);\n        }, 200);\n      }\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    if (this._destroyed) {\n      return of(undefined);\n    }\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n      if (this._animationsDisabled) {\n        afterNextRender(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n        }, {\n          injector: this._injector\n        });\n      } else {\n        clearTimeout(this._exitFallback);\n        this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n      }\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n  }\n  _completeExit() {\n    clearTimeout(this._exitFallback);\n    queueMicrotask(() => {\n      this._onExit.next();\n      this._onExit.complete();\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (this._announceTimeoutId) {\n      return;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      this._announceTimeoutId = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n        const element = this._elementRef.nativeElement;\n        const inertElement = element.querySelector('[aria-hidden]');\n        const liveElement = element.querySelector('[aria-live]');\n        if (inertElement && liveElement) {\n          // If an element in the snack bar content is focused before being moved\n          // track it and restore focus after moving to the live region.\n          let focusedElement = null;\n          if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n            focusedElement = document.activeElement;\n          }\n          inertElement.removeAttribute('aria-hidden');\n          liveElement.appendChild(inertElement);\n          focusedElement?.focus();\n          this._onAnnounce.next();\n          this._onAnnounce.complete();\n        }\n      }, this._announceDelay);\n    });\n  }\n  static ɵfac = function MatSnackBarContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSnackBarContainer,\n    selectors: [[\"mat-snack-bar-container\"]],\n    viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\"],\n    hostVars: 6,\n    hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"animationend\", function MatSnackBarContainer_animationend_HostBindingHandler($event) {\n          return ctx.onAnimationEnd($event.animationName);\n        })(\"animationcancel\", function MatSnackBarContainer_animationcancel_HostBindingHandler($event) {\n          return ctx.onAnimationEnd($event.animationName);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-snack-bar-container-enter\", ctx._animationState === \"visible\")(\"mat-snack-bar-container-exit\", ctx._animationState === \"hidden\")(\"mat-snack-bar-container-animations-enabled\", !ctx._animationsDisabled);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 6,\n    vars: 3,\n    consts: [[\"label\", \"\"], [1, \"mdc-snackbar__surface\", \"mat-mdc-snackbar-surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n    template: function MatSnackBarContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0)(3, \"div\", 3);\n        i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"div\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n        '(animationend)': 'onAnimationEnd($event.animationName)',\n        '(animationcancel)': 'onAnimationEnd($event.animationName)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n  _live = inject(LiveAnnouncer);\n  _injector = inject(Injector);\n  _breakpointObserver = inject(BreakpointObserver);\n  _parentSnackBar = inject(MatSnackBar, {\n    optional: true,\n    skipSelf: true\n  });\n  _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n  _animationsDisabled = _animationsDisabled();\n  /**\n   * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n   * If there is a parent snack-bar service, all operations should delegate to that parent\n   * via `_openedSnackBarRef`.\n   */\n  _snackBarRefAtThisLevel = null;\n  /** The component that should be rendered as the snack bar's simple component. */\n  simpleSnackBarComponent = SimpleSnackBar;\n  /** The container component that attaches the provided template or component. */\n  snackBarContainerComponent = MatSnackBarContainer;\n  /** The CSS class to apply for handset mode. */\n  handsetCssClass = 'mat-mdc-snack-bar-handset';\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor() {}\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = {\n      ...this._defaultConfig,\n      ...config\n    };\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = {\n      ...new MatSnackBarConfig(),\n      ...this._defaultConfig,\n      ...userConfig\n    };\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    const positionStrategy = createGlobalPositionStrategy(this._injector);\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    overlayConfig.disableAnimations = this._animationsDisabled;\n    return createOverlayRef(this._injector, overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n  static ɵfac = function MatSnackBar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBar)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatSnackBar,\n    factory: MatSnackBar.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n  static ɵfac = function MatSnackBarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSnackBarModule,\n    imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n    exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatSnackBar],\n    imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatSnackBar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n  // Represents\n  // trigger('state', [\n  //   state(\n  //     'void, hidden',\n  //     style({\n  //       transform: 'scale(0.8)',\n  //       opacity: 0,\n  //     }),\n  //   ),\n  //   state(\n  //     'visible',\n  //     style({\n  //       transform: 'scale(1)',\n  //       opacity: 1,\n  //     }),\n  //   ),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition(\n  //     '* => void, * => hidden',\n  //     animate(\n  //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n  //       style({\n  //         opacity: 0,\n  //       }),\n  //     ),\n  //   ),\n  // ])\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: {\n    type: 7,\n    name: 'state',\n    'definitions': [{\n      type: 0,\n      name: 'void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(0.8)',\n          opacity: 0\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(1)',\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => visible',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void, * => hidden',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,UAAU,CAAC;AAC7C,IAAG,WAAW,SAAS,SAAS,gEAAgE;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,OAAO,KAAK,QAAQ,GAAG;AAAA,EACpD;AACF;AACA,IAAM,MAAM,CAAC,OAAO;AACpB,SAAS,4CAA4C,IAAI,KAAK;AAAC;AAC/D,IAAM,cAAc,KAAK,IAAI,GAAG,EAAE,IAAI;AAItC,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,kBAAkB,IAAI,QAAQ;AAAA;AAAA,EAE9B,eAAe,IAAI,QAAQ;AAAA;AAAA,EAE3B,YAAY,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB;AAAA;AAAA,EAEA,qBAAqB;AAAA,EACrB,YAAY,mBAAmB,aAAa;AAC1C,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,sBAAkB,QAAQ,UAAU,MAAM,KAAK,eAAe,CAAC;AAAA,EACjE;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,CAAC,KAAK,gBAAgB,QAAQ;AAChC,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AACA,iBAAa,KAAK,kBAAkB;AAAA,EACtC;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,WAAK,qBAAqB;AAC1B,WAAK,UAAU,KAAK;AACpB,WAAK,UAAU,SAAS;AACxB,WAAK,QAAQ;AAAA,IACf;AACA,iBAAa,KAAK,kBAAkB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc,UAAU;AAGtB,SAAK,qBAAqB,WAAW,MAAM,KAAK,QAAQ,GAAG,KAAK,IAAI,UAAU,WAAW,CAAC;AAAA,EAC5F;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,aAAa,QAAQ;AAC7B,WAAK,aAAa,KAAK;AACvB,WAAK,aAAa,SAAS;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,YAAY,QAAQ;AACzB,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,WAAK,UAAU,SAAS;AAAA,IAC1B;AACA,SAAK,gBAAgB,KAAK;AAAA,MACxB,mBAAmB,KAAK;AAAA,IAC1B,CAAC;AACD,SAAK,gBAAgB,SAAS;AAC9B,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,kBAAkB;AAAA,EAChC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AAGA,IAAM,qBAAqB,IAAI,eAAe,iBAAiB;AAI/D,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAEtB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA,EAEX;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,qBAAqB;AAAA;AAAA,EAErB,mBAAmB;AACrB;AAGA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,WAAW,CAAC,GAAG,2BAA2B,qBAAqB;AAAA,EACjE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC1C,WAAW,CAAC,GAAG,6BAA6B,uBAAuB;AAAA,EACrE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,WAAW,CAAC,GAAG,4BAA4B,sBAAsB;AAAA,EACnE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc,OAAO,cAAc;AAAA,EACnC,OAAO,OAAO,kBAAkB;AAAA,EAChC,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,SAAS;AACP,SAAK,YAAY,kBAAkB;AAAA,EACrC;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,CAAC,CAAC,KAAK,KAAK;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,WAAW,CAAC,GAAG,0BAA0B;AAAA,IACzC,UAAU,CAAC,aAAa;AAAA,IACxB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,aAAa,IAAI,qBAAqB,IAAI,GAAG,OAAO,CAAC;AAAA,IACrH,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAChB,QAAG,oBAAoB,GAAG,uCAAuC,GAAG,GAAG,OAAO,CAAC;AAAA,MACjF;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU;AACb,QAAG,mBAAmB,KAAK,IAAI,KAAK,SAAS,IAAI;AACjD,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,YAAY,IAAI,EAAE;AAAA,MACzC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,kBAAkB,oBAAoB,iBAAiB;AAAA,IACjF,QAAQ,CAAC,2CAA2C;AAAA,IACpD,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,WAAW,kBAAkB,oBAAoB,iBAAiB;AAAA,MAC5E,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,2CAA2C;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AAKvB,IAAM,uBAAN,MAAM,8BAA6B,iBAAiB;AAAA,EAClD,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,YAAY,OAAO,QAAQ;AAAA,EAC3B,sBAAsB,oBAAoB;AAAA,EAC1C,iBAAiB,OAAO,iBAAiB;AAAA,EACzC,YAAY,OAAO,QAAQ;AAAA,EAC3B,iBAAiB,oBAAI,IAAI;AAAA,EACzB;AAAA,EACA;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb;AAAA;AAAA,EAEA,cAAc,IAAI,QAAQ;AAAA;AAAA,EAE1B,UAAU,IAAI,QAAQ;AAAA;AAAA,EAEtB,WAAW,IAAI,QAAQ;AAAA;AAAA,EAEvB,kBAAkB;AAAA;AAAA,EAElB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,iBAAiB,OAAO,YAAY,EAAE,MAAM,+BAA+B;AAAA,EAC3E,cAAc;AACZ,UAAM;AACN,UAAM,SAAS,KAAK;AAGpB,QAAI,OAAO,eAAe,eAAe,CAAC,OAAO,qBAAqB;AACpE,WAAK,QAAQ;AAAA,IACf,WAAW,OAAO,eAAe,OAAO;AACtC,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAGA,QAAI,KAAK,UAAU,SAAS;AAC1B,UAAI,KAAK,UAAU,UAAU;AAC3B,aAAK,QAAQ;AAAA,MACf;AACA,UAAI,KAAK,UAAU,aAAa;AAC9B,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB,QAAQ;AAC5B,SAAK,mBAAmB;AACxB,UAAM,SAAS,KAAK,cAAc,sBAAsB,MAAM;AAC9D,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB,QAAQ;AAC3B,SAAK,mBAAmB;AACxB,UAAM,SAAS,KAAK,cAAc,qBAAqB,MAAM;AAC7D,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,YAAU;AAC1B,SAAK,mBAAmB;AACxB,UAAM,SAAS,KAAK,cAAc,gBAAgB,MAAM;AACxD,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,eAAe,eAAe;AAC5B,QAAI,kBAAkB,gBAAgB;AACpC,WAAK,cAAc;AAAA,IACrB,WAAW,kBAAkB,iBAAiB;AAC5C,mBAAa,KAAK,cAAc;AAChC,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,SAAS,KAAK;AACnB,aAAK,SAAS,SAAS;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,kBAAkB;AAGvB,WAAK,mBAAmB,aAAa;AACrC,WAAK,mBAAmB,cAAc;AACtC,WAAK,sBAAsB;AAC3B,UAAI,KAAK,qBAAqB;AAC5B,wBAAgB,MAAM;AACpB,eAAK,QAAQ,IAAI,MAAM,eAAe,MAAM,KAAK,eAAe,eAAe,CAAC,CAAC;AAAA,QACnF,GAAG;AAAA,UACD,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH,OAAO;AACL,qBAAa,KAAK,cAAc;AAChC,aAAK,iBAAiB,WAAW,MAAM;AAGrC,eAAK,YAAY,cAAc,UAAU,IAAI,gCAAgC;AAC7E,eAAK,eAAe,eAAe;AAAA,QACrC,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,KAAK,YAAY;AACnB,aAAO,GAAG,MAAS;AAAA,IACrB;AAGA,SAAK,QAAQ,IAAI,MAAM;AAIrB,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,aAAa;AAIrC,WAAK,YAAY,cAAc,aAAa,YAAY,EAAE;AAG1D,mBAAa,KAAK,kBAAkB;AACpC,UAAI,KAAK,qBAAqB;AAC5B,wBAAgB,MAAM;AACpB,eAAK,QAAQ,IAAI,MAAM,eAAe,MAAM,KAAK,eAAe,cAAc,CAAC,CAAC;AAAA,QAClF,GAAG;AAAA,UACD,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH,OAAO;AACL,qBAAa,KAAK,aAAa;AAC/B,aAAK,gBAAgB,WAAW,MAAM,KAAK,eAAe,cAAc,GAAG,GAAG;AAAA,MAChF;AAAA,IACF,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,gBAAgB;AACd,iBAAa,KAAK,aAAa;AAC/B,mBAAe,MAAM;AACnB,WAAK,QAAQ,KAAK;AAClB,WAAK,QAAQ,SAAS;AAAA,IACxB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,eAAe,KAAK,eAAe;AACzC,QAAI,cAAc;AAChB,UAAI,MAAM,QAAQ,YAAY,GAAG;AAE/B,qBAAa,QAAQ,cAAY,QAAQ,UAAU,IAAI,QAAQ,CAAC;AAAA,MAClE,OAAO;AACL,gBAAQ,UAAU,IAAI,YAAY;AAAA,MACpC;AAAA,IACF;AACA,SAAK,gBAAgB;AAIrB,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,aAAa;AACnB,UAAM,UAAU,OAAO,YAAY,CAAC,MAAM,cAAc,IAAI,UAAU,EAAE,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAOhB,UAAM,KAAK,KAAK;AAChB,UAAM,SAAS,KAAK,UAAU,iBAAiB,mDAAmD;AAClG,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,WAAK,eAAe,IAAI,KAAK;AAC7B,UAAI,CAAC,UAAU;AACb,cAAM,aAAa,aAAa,EAAE;AAAA,MACpC,WAAW,SAAS,QAAQ,EAAE,MAAM,IAAI;AACtC,cAAM,aAAa,aAAa,WAAW,MAAM,EAAE;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,eAAe,QAAQ,WAAS;AACnC,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,UAAI,UAAU;AACZ,cAAM,WAAW,SAAS,QAAQ,KAAK,gBAAgB,EAAE,EAAE,KAAK;AAChE,YAAI,SAAS,SAAS,GAAG;AACvB,gBAAM,aAAa,aAAa,QAAQ;AAAA,QAC1C,OAAO;AACL,gBAAM,gBAAgB,WAAW;AAAA,QACnC;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,eAAe,MAAM;AAAA,EAC5B;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,KAAK,cAAc,YAAY,MAAM,OAAO,cAAc,eAAe,YAAY;AACvF,YAAM,MAAM,0EAA0E;AAAA,IACxF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,qBAAqB,WAAW,MAAM;AACzC,YAAI,KAAK,YAAY;AACnB;AAAA,QACF;AACA,cAAM,UAAU,KAAK,YAAY;AACjC,cAAM,eAAe,QAAQ,cAAc,eAAe;AAC1D,cAAM,cAAc,QAAQ,cAAc,aAAa;AACvD,YAAI,gBAAgB,aAAa;AAG/B,cAAI,iBAAiB;AACrB,cAAI,KAAK,UAAU,aAAa,SAAS,yBAAyB,eAAe,aAAa,SAAS,SAAS,aAAa,GAAG;AAC9H,6BAAiB,SAAS;AAAA,UAC5B;AACA,uBAAa,gBAAgB,aAAa;AAC1C,sBAAY,YAAY,YAAY;AACpC,0BAAgB,MAAM;AACtB,eAAK,YAAY,KAAK;AACtB,eAAK,YAAY,SAAS;AAAA,QAC5B;AAAA,MACF,GAAG,KAAK,cAAc;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,IACvC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC;AACjC,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,gBAAgB,6BAA6B;AAAA,IAC5D,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,gBAAgB,SAAS,qDAAqD,QAAQ;AAClG,iBAAO,IAAI,eAAe,OAAO,aAAa;AAAA,QAChD,CAAC,EAAE,mBAAmB,SAAS,wDAAwD,QAAQ;AAC7F,iBAAO,IAAI,eAAe,OAAO,aAAa;AAAA,QAChD,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iCAAiC,IAAI,oBAAoB,SAAS,EAAE,gCAAgC,IAAI,oBAAoB,QAAQ,EAAE,8CAA8C,CAAC,IAAI,mBAAmB;AAAA,MAC7N;AAAA,IACF;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,yBAAyB,0BAA0B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,IAClK,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1D,QAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,eAAe,CAAC;AACpF,QAAG,aAAa;AAChB,QAAG,UAAU,GAAG,KAAK;AACrB,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,aAAa,IAAI,KAAK,EAAE,QAAQ,IAAI,KAAK,EAAE,MAAM,IAAI,cAAc;AAAA,MACpF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe;AAAA,IAC9B,QAAQ,CAAC,83FAA83F;AAAA,IACv4F,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,eAAe;AAAA,MACzB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,yCAAyC;AAAA,QACzC,wCAAwC;AAAA,QACxC,sDAAsD;AAAA,QACtD,kBAAkB;AAAA,QAClB,qBAAqB;AAAA,MACvB;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,83FAA83F;AAAA,IACz4F,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,SAAS,wCAAwC;AAC/C,SAAO,IAAI,kBAAkB;AAC/B;AAEA,IAAM,gCAAgC,IAAI,eAAe,iCAAiC;AAAA,EACxF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAID,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,QAAQ,OAAO,aAAa;AAAA,EAC5B,YAAY,OAAO,QAAQ;AAAA,EAC3B,sBAAsB,OAAO,kBAAkB;AAAA,EAC/C,kBAAkB,OAAO,cAAa;AAAA,IACpC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,iBAAiB,OAAO,6BAA6B;AAAA,EACrD,sBAAsB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1C,0BAA0B;AAAA;AAAA,EAE1B,0BAA0B;AAAA;AAAA,EAE1B,6BAA6B;AAAA;AAAA,EAE7B,kBAAkB;AAAA;AAAA,EAElB,IAAI,qBAAqB;AACvB,UAAM,SAAS,KAAK;AACpB,WAAO,SAAS,OAAO,qBAAqB,KAAK;AAAA,EACnD;AAAA,EACA,IAAI,mBAAmB,OAAO;AAC5B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,qBAAqB;AAAA,IAC5C,OAAO;AACL,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQf,kBAAkB,WAAW,QAAQ;AACnC,WAAO,KAAK,QAAQ,WAAW,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,UAAU,QAAQ;AACjC,WAAO,KAAK,QAAQ,UAAU,MAAM;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,SAAS,SAAS,IAAI,QAAQ;AACjC,UAAM,UAAU,kCACX,KAAK,iBACL;AAIL,YAAQ,OAAO;AAAA,MACb;AAAA,MACA;AAAA,IACF;AAGA,QAAI,QAAQ,wBAAwB,SAAS;AAC3C,cAAQ,sBAAsB;AAAA,IAChC;AACA,WAAO,KAAK,kBAAkB,KAAK,yBAAyB,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB,QAAQ;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AAEZ,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB,QAAQ;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB,YAAY,QAAQ;AAC3C,UAAM,eAAe,UAAU,OAAO,oBAAoB,OAAO,iBAAiB;AAClF,UAAM,WAAW,SAAS,OAAO;AAAA,MAC/B,QAAQ,gBAAgB,KAAK;AAAA,MAC7B,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AACD,UAAM,kBAAkB,IAAI,gBAAgB,KAAK,4BAA4B,OAAO,kBAAkB,QAAQ;AAC9G,UAAM,eAAe,WAAW,OAAO,eAAe;AACtD,iBAAa,SAAS,iBAAiB;AACvC,WAAO,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,SAAS,YAAY;AAC3B,UAAM,SAAS,iDACV,IAAI,kBAAkB,IACtB,KAAK,iBACL;AAEL,UAAM,aAAa,KAAK,eAAe,MAAM;AAC7C,UAAM,YAAY,KAAK,yBAAyB,YAAY,MAAM;AAClE,UAAM,cAAc,IAAI,eAAe,WAAW,UAAU;AAC5D,QAAI,mBAAmB,aAAa;AAClC,YAAM,SAAS,IAAI,eAAe,SAAS,MAAM;AAAA,QAC/C,WAAW,OAAO;AAAA,QAClB;AAAA,MACF,CAAC;AACD,kBAAY,WAAW,UAAU,qBAAqB,MAAM;AAAA,IAC9D,OAAO;AACL,YAAM,WAAW,KAAK,gBAAgB,QAAQ,WAAW;AACzD,YAAM,SAAS,IAAI,gBAAgB,SAAS,QAAW,QAAQ;AAC/D,YAAM,aAAa,UAAU,sBAAsB,MAAM;AAEzD,kBAAY,WAAW,WAAW;AAAA,IACpC;AAIA,SAAK,oBAAoB,QAAQ,YAAY,eAAe,EAAE,KAAK,UAAU,WAAW,YAAY,CAAC,CAAC,EAAE,UAAU,WAAS;AACzH,iBAAW,eAAe,UAAU,OAAO,KAAK,iBAAiB,MAAM,OAAO;AAAA,IAChF,CAAC;AACD,QAAI,OAAO,qBAAqB;AAE9B,gBAAU,YAAY,UAAU,MAAM;AACpC,aAAK,MAAM,SAAS,OAAO,qBAAqB,OAAO,UAAU;AAAA,MACnE,CAAC;AAAA,IACH;AACA,SAAK,iBAAiB,aAAa,MAAM;AACzC,SAAK,qBAAqB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB,aAAa,QAAQ;AAEpC,gBAAY,eAAe,EAAE,UAAU,MAAM;AAE3C,UAAI,KAAK,sBAAsB,aAAa;AAC1C,aAAK,qBAAqB;AAAA,MAC5B;AACA,UAAI,OAAO,qBAAqB;AAC9B,aAAK,MAAM,MAAM;AAAA,MACnB;AAAA,IACF,CAAC;AAED,QAAI,OAAO,YAAY,OAAO,WAAW,GAAG;AAC1C,kBAAY,YAAY,EAAE,UAAU,MAAM,YAAY,cAAc,OAAO,QAAQ,CAAC;AAAA,IACtF;AACA,QAAI,KAAK,oBAAoB;AAG3B,WAAK,mBAAmB,eAAe,EAAE,UAAU,MAAM;AACvD,oBAAY,kBAAkB,MAAM;AAAA,MACtC,CAAC;AACD,WAAK,mBAAmB,QAAQ;AAAA,IAClC,OAAO;AAEL,kBAAY,kBAAkB,MAAM;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,QAAQ;AACrB,UAAM,gBAAgB,IAAI,cAAc;AACxC,kBAAc,YAAY,OAAO;AACjC,UAAM,mBAAmB,6BAA6B,KAAK,SAAS;AAEpE,UAAM,QAAQ,OAAO,cAAc;AACnC,UAAM,SAAS,OAAO,uBAAuB,UAAU,OAAO,uBAAuB,WAAW,CAAC,SAAS,OAAO,uBAAuB,SAAS;AACjJ,UAAM,UAAU,CAAC,UAAU,OAAO,uBAAuB;AACzD,QAAI,QAAQ;AACV,uBAAiB,KAAK,GAAG;AAAA,IAC3B,WAAW,SAAS;AAClB,uBAAiB,MAAM,GAAG;AAAA,IAC5B,OAAO;AACL,uBAAiB,mBAAmB;AAAA,IACtC;AAEA,QAAI,OAAO,qBAAqB,OAAO;AACrC,uBAAiB,IAAI,GAAG;AAAA,IAC1B,OAAO;AACL,uBAAiB,OAAO,GAAG;AAAA,IAC7B;AACA,kBAAc,mBAAmB;AACjC,kBAAc,oBAAoB,KAAK;AACvC,WAAO,iBAAiB,KAAK,WAAW,aAAa;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,QAAQ,aAAa;AACnC,UAAM,eAAe,UAAU,OAAO,oBAAoB,OAAO,iBAAiB;AAClF,WAAO,SAAS,OAAO;AAAA,MACrB,QAAQ,gBAAgB,KAAK;AAAA,MAC7B,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,aAAY;AAAA,IACrB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,aAAa,CAAC,sBAAsB,kBAAkB,oBAAoB,iBAAiB;AACjG,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,cAAc,iBAAiB,iBAAiB,gBAAgB,sBAAsB,kBAAkB,oBAAoB,iBAAiB;AAAA,IACtK,SAAS,CAAC,iBAAiB,sBAAsB,kBAAkB,oBAAoB,iBAAiB;AAAA,EAC1G,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,WAAW;AAAA,IACvB,SAAS,CAAC,eAAe,cAAc,iBAAiB,iBAAiB,gBAAgB,eAAe;AAAA,EAC1G,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,cAAc,iBAAiB,iBAAiB,gBAAgB,GAAG,UAAU;AAAA,MACtG,SAAS,CAAC,iBAAiB,GAAG,UAAU;AAAA,MACxC,WAAW,CAAC,WAAW;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6B5B,eAAe;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,SAAS;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}