{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/form-field-C9DZXojn.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Input, ElementRef, NgZone, Renderer2, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ChangeDetectorRef, viewChild, computed, contentChild, signal, afterRenderEffect, ContentChild, ContentChildren } from '@angular/core';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { startWith, map, pairwise, filter, takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** The floating label for a `mat-form-field`. */\nconst _c0 = [\"notch\"];\nconst _c1 = [\"matFormFieldNotchedOutline\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = [\"iconPrefixContainer\"];\nconst _c4 = [\"textPrefixContainer\"];\nconst _c5 = [\"iconSuffixContainer\"];\nconst _c6 = [\"textSuffixContainer\"];\nconst _c7 = [\"textField\"];\nconst _c8 = [\"*\", [[\"mat-label\"]], [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"]], [[\"\", \"matTextPrefix\", \"\"]], [[\"\", \"matTextSuffix\", \"\"]], [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"]], [[\"mat-error\"], [\"\", \"matError\", \"\"]], [[\"mat-hint\", 3, \"align\", \"end\"]], [[\"mat-hint\", \"align\", \"end\"]]];\nconst _c9 = [\"*\", \"mat-label\", \"[matPrefix], [matIconPrefix]\", \"[matTextPrefix]\", \"[matTextSuffix]\", \"[matSuffix], [matIconSuffix]\", \"mat-error, [matError]\", \"mat-hint:not([align='end'])\", \"mat-hint[align='end']\"];\nfunction MatFormField_ng_template_0_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n}\nfunction MatFormField_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 19);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵconditionalCreate(2, MatFormField_ng_template_0_Conditional_0_Conditional_2_Template, 1, 0, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"floating\", ctx_r1._shouldLabelFloat())(\"monitorResize\", ctx_r1._hasOutline())(\"id\", ctx_r1._labelId);\n    i0.ɵɵattribute(\"for\", ctx_r1._control.disableAutomaticLabeling ? null : ctx_r1._control.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(!ctx_r1.hideRequiredMarker && ctx_r1._control.required ? 2 : -1);\n  }\n}\nfunction MatFormField_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, MatFormField_ng_template_0_Conditional_0_Template, 3, 5, \"label\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1._hasFloatingLabel() ? 0 : -1);\n  }\n}\nfunction MatFormField_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n}\nfunction MatFormField_Conditional_6_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_Conditional_6_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_Conditional_6_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction MatFormField_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵconditionalCreate(1, MatFormField_Conditional_6_Conditional_1_Template, 1, 1, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matFormFieldNotchedOutlineOpen\", ctx_r1._shouldLabelFloat());\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r1._forceDisplayInfixLabel() ? 1 : -1);\n  }\n}\nfunction MatFormField_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10, 2);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵprojection(2, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_10_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_Conditional_10_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction MatFormField_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14, 4);\n    i0.ɵɵprojection(2, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15, 5);\n    i0.ɵɵprojection(2, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n}\nfunction MatFormField_Case_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 6);\n  }\n}\nfunction MatFormField_Case_18_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", ctx_r1._hintLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.hintLabel);\n  }\n}\nfunction MatFormField_Case_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, MatFormField_Case_18_Conditional_0_Template, 2, 2, \"mat-hint\", 21);\n    i0.ɵɵprojection(1, 7);\n    i0.ɵɵelement(2, \"div\", 22);\n    i0.ɵɵprojection(3, 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.hintLabel ? 0 : -1);\n  }\n}\nclass MatLabel {\n  static ɵfac = function MatLabel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatLabel)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatLabel,\n    selectors: [[\"mat-label\"]]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLabel, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-label'\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nclass MatError {\n  id = inject(_IdGenerator).getId('mat-mdc-error-');\n  constructor() {}\n  static ɵfac = function MatError_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatError)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatError,\n    selectors: [[\"mat-error\"], [\"\", \"matError\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-form-field-error\", \"mat-mdc-form-field-bottom-align\"],\n    hostVars: 1,\n    hostBindings: function MatError_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_ERROR,\n      useExisting: MatError\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatError, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-error, [matError]',\n      host: {\n        'class': 'mat-mdc-form-field-error mat-mdc-form-field-bottom-align',\n        '[id]': 'id'\n      },\n      providers: [{\n        provide: MAT_ERROR,\n        useExisting: MatError\n      }]\n    }]\n  }], () => [], {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Hint text to be shown underneath the form field control. */\nclass MatHint {\n  /** Whether to align the hint label at the start or end of the line. */\n  align = 'start';\n  /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n  id = inject(_IdGenerator).getId('mat-mdc-hint-');\n  static ɵfac = function MatHint_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatHint)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatHint,\n    selectors: [[\"mat-hint\"]],\n    hostAttrs: [1, \"mat-mdc-form-field-hint\", \"mat-mdc-form-field-bottom-align\"],\n    hostVars: 4,\n    hostBindings: function MatHint_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"align\", null);\n        i0.ɵɵclassProp(\"mat-mdc-form-field-hint-end\", ctx.align === \"end\");\n      }\n    },\n    inputs: {\n      align: \"align\",\n      id: \"id\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHint, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-hint',\n      host: {\n        'class': 'mat-mdc-form-field-hint mat-mdc-form-field-bottom-align',\n        '[class.mat-mdc-form-field-hint-end]': 'align === \"end\"',\n        '[id]': 'id',\n        // Remove align attribute to prevent it from interfering with layout.\n        '[attr.align]': 'null'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nclass MatPrefix {\n  set _isTextSelector(value) {\n    this._isText = true;\n  }\n  _isText = false;\n  static ɵfac = function MatPrefix_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPrefix)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatPrefix,\n    selectors: [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"], [\"\", \"matTextPrefix\", \"\"]],\n    inputs: {\n      _isTextSelector: [0, \"matTextPrefix\", \"_isTextSelector\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_PREFIX,\n      useExisting: MatPrefix\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPrefix, [{\n    type: Directive,\n    args: [{\n      selector: '[matPrefix], [matIconPrefix], [matTextPrefix]',\n      providers: [{\n        provide: MAT_PREFIX,\n        useExisting: MatPrefix\n      }]\n    }]\n  }], null, {\n    _isTextSelector: [{\n      type: Input,\n      args: ['matTextPrefix']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nclass MatSuffix {\n  set _isTextSelector(value) {\n    this._isText = true;\n  }\n  _isText = false;\n  static ɵfac = function MatSuffix_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSuffix)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSuffix,\n    selectors: [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"], [\"\", \"matTextSuffix\", \"\"]],\n    inputs: {\n      _isTextSelector: [0, \"matTextSuffix\", \"_isTextSelector\"]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_SUFFIX,\n      useExisting: MatSuffix\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSuffix, [{\n    type: Directive,\n    args: [{\n      selector: '[matSuffix], [matIconSuffix], [matTextSuffix]',\n      providers: [{\n        provide: MAT_SUFFIX,\n        useExisting: MatSuffix\n      }]\n    }]\n  }], null, {\n    _isTextSelector: [{\n      type: Input,\n      args: ['matTextSuffix']\n    }]\n  });\n})();\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nclass MatFormFieldFloatingLabel {\n  _elementRef = inject(ElementRef);\n  /** Whether the label is floating. */\n  get floating() {\n    return this._floating;\n  }\n  set floating(value) {\n    this._floating = value;\n    if (this.monitorResize) {\n      this._handleResize();\n    }\n  }\n  _floating = false;\n  /** Whether to monitor for resize events on the floating label. */\n  get monitorResize() {\n    return this._monitorResize;\n  }\n  set monitorResize(value) {\n    this._monitorResize = value;\n    if (this._monitorResize) {\n      this._subscribeToResize();\n    } else {\n      this._resizeSubscription.unsubscribe();\n    }\n  }\n  _monitorResize = false;\n  /** The shared ResizeObserver. */\n  _resizeObserver = inject(SharedResizeObserver);\n  /** The Angular zone. */\n  _ngZone = inject(NgZone);\n  /** The parent form-field. */\n  _parent = inject(FLOATING_LABEL_PARENT);\n  /** The current resize event subscription. */\n  _resizeSubscription = new Subscription();\n  constructor() {}\n  ngOnDestroy() {\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Gets the width of the label. Used for the outline notch. */\n  getWidth() {\n    return estimateScrollWidth(this._elementRef.nativeElement);\n  }\n  /** Gets the HTML element for the floating label. */\n  get element() {\n    return this._elementRef.nativeElement;\n  }\n  /** Handles resize events from the ResizeObserver. */\n  _handleResize() {\n    // In the case where the label grows in size, the following sequence of events occurs:\n    // 1. The label grows by 1px triggering the ResizeObserver\n    // 2. The notch is expanded to accommodate the entire label\n    // 3. The label expands to its full width, triggering the ResizeObserver again\n    //\n    // This is expected, but If we allow this to all happen within the same macro task it causes an\n    // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n    // the next macro task.\n    setTimeout(() => this._parent._handleLabelResized());\n  }\n  /** Subscribes to resize events. */\n  _subscribeToResize() {\n    this._resizeSubscription.unsubscribe();\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeSubscription = this._resizeObserver.observe(this._elementRef.nativeElement, {\n        box: 'border-box'\n      }).subscribe(() => this._handleResize());\n    });\n  }\n  static ɵfac = function MatFormFieldFloatingLabel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFormFieldFloatingLabel)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatFormFieldFloatingLabel,\n    selectors: [[\"label\", \"matFormFieldFloatingLabel\", \"\"]],\n    hostAttrs: [1, \"mdc-floating-label\", \"mat-mdc-floating-label\"],\n    hostVars: 2,\n    hostBindings: function MatFormFieldFloatingLabel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-floating-label--float-above\", ctx.floating);\n      }\n    },\n    inputs: {\n      floating: \"floating\",\n      monitorResize: \"monitorResize\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldFloatingLabel, [{\n    type: Directive,\n    args: [{\n      selector: 'label[matFormFieldFloatingLabel]',\n      host: {\n        'class': 'mdc-floating-label mat-mdc-floating-label',\n        '[class.mdc-floating-label--float-above]': 'floating'\n      }\n    }]\n  }], () => [], {\n    floating: [{\n      type: Input\n    }],\n    monitorResize: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n  const clone = htmlEl.cloneNode(true);\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  clone.remove();\n  return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nclass MatFormFieldLineRipple {\n  _elementRef = inject(ElementRef);\n  _cleanupTransitionEnd;\n  constructor() {\n    const ngZone = inject(NgZone);\n    const renderer = inject(Renderer2);\n    ngZone.runOutsideAngular(() => {\n      this._cleanupTransitionEnd = renderer.listen(this._elementRef.nativeElement, 'transitionend', this._handleTransitionEnd);\n    });\n  }\n  activate() {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(DEACTIVATING_CLASS);\n    classList.add(ACTIVATE_CLASS);\n  }\n  deactivate() {\n    this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n  }\n  _handleTransitionEnd = event => {\n    const classList = this._elementRef.nativeElement.classList;\n    const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n    if (event.propertyName === 'opacity' && isDeactivating) {\n      classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n    }\n  };\n  ngOnDestroy() {\n    this._cleanupTransitionEnd();\n  }\n  static ɵfac = function MatFormFieldLineRipple_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFormFieldLineRipple)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatFormFieldLineRipple,\n    selectors: [[\"div\", \"matFormFieldLineRipple\", \"\"]],\n    hostAttrs: [1, \"mdc-line-ripple\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldLineRipple, [{\n    type: Directive,\n    args: [{\n      selector: 'div[matFormFieldLineRipple]',\n      host: {\n        'class': 'mdc-line-ripple'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nclass MatFormFieldNotchedOutline {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  /** Whether the notch should be opened. */\n  open = false;\n  _notch;\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    const label = element.querySelector('.mdc-floating-label');\n    if (label) {\n      element.classList.add('mdc-notched-outline--upgraded');\n      if (typeof requestAnimationFrame === 'function') {\n        label.style.transitionDuration = '0s';\n        this._ngZone.runOutsideAngular(() => {\n          requestAnimationFrame(() => label.style.transitionDuration = '');\n        });\n      }\n    } else {\n      element.classList.add('mdc-notched-outline--no-label');\n    }\n  }\n  _setNotchWidth(labelWidth) {\n    const notch = this._notch.nativeElement;\n    if (!this.open || !labelWidth) {\n      notch.style.width = '';\n    } else {\n      const NOTCH_ELEMENT_PADDING = 8;\n      const NOTCH_ELEMENT_BORDER = 1;\n      notch.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n    }\n  }\n  _setMaxWidth(prefixAndSuffixWidth) {\n    // Set this only on the notch to avoid style recalculations in other parts of the form field.\n    this._notch.nativeElement.style.setProperty('--mat-form-field-notch-max-width', `calc(100% - ${prefixAndSuffixWidth}px)`);\n  }\n  static ɵfac = function MatFormFieldNotchedOutline_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFormFieldNotchedOutline)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatFormFieldNotchedOutline,\n    selectors: [[\"div\", \"matFormFieldNotchedOutline\", \"\"]],\n    viewQuery: function MatFormFieldNotchedOutline_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notch = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mdc-notched-outline\"],\n    hostVars: 2,\n    hostBindings: function MatFormFieldNotchedOutline_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-notched-outline--notched\", ctx.open);\n      }\n    },\n    inputs: {\n      open: [0, \"matFormFieldNotchedOutlineOpen\", \"open\"]\n    },\n    attrs: _c1,\n    ngContentSelectors: _c2,\n    decls: 5,\n    vars: 0,\n    consts: [[\"notch\", \"\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__leading\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__notch\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__trailing\"]],\n    template: function MatFormFieldNotchedOutline_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2, 0);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"div\", 3);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldNotchedOutline, [{\n    type: Component,\n    args: [{\n      selector: 'div[matFormFieldNotchedOutline]',\n      host: {\n        'class': 'mdc-notched-outline',\n        // Besides updating the notch state through the MDC component, we toggle this class through\n        // a host binding in order to ensure that the notched-outline renders correctly on the server.\n        '[class.mdc-notched-outline--notched]': 'open'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__trailing\\\"></div>\\n\"\n    }]\n  }], null, {\n    open: [{\n      type: Input,\n      args: ['matFormFieldNotchedOutlineOpen']\n    }],\n    _notch: [{\n      type: ViewChild,\n      args: ['notch']\n    }]\n  });\n})();\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nclass MatFormFieldControl {\n  /** The value of the control. */\n  value;\n  /**\n   * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n   * needs to run change detection.\n   */\n  stateChanges;\n  /** The element ID for this control. */\n  id;\n  /** The placeholder for this control. */\n  placeholder;\n  /** Gets the AbstractControlDirective for this control. */\n  ngControl;\n  /** Whether the control is focused. */\n  focused;\n  /** Whether the control is empty. */\n  empty;\n  /** Whether the `MatFormField` label should try to float. */\n  shouldLabelFloat;\n  /** Whether the control is required. */\n  required;\n  /** Whether the control is disabled. */\n  disabled;\n  /** Whether the control is in an error state. */\n  errorState;\n  /**\n   * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n   * based on their control type. The form field will add a class,\n   * `mat-form-field-type-{{controlType}}` to its root element.\n   */\n  controlType;\n  /**\n   * Whether the input is currently in an autofilled state. If property is not present on the\n   * control it is assumed to be false.\n   */\n  autofilled;\n  /**\n   * Value of `aria-describedby` that should be merged with the described-by ids\n   * which are set by the form-field.\n   */\n  userAriaDescribedBy;\n  /**\n   * Whether to automatically assign the ID of the form field as the `for` attribute\n   * on the `<label>` inside the form field. Set this to true to prevent the form\n   * field from associating the label with non-native elements.\n   */\n  disableAutomaticLabeling;\n  /** Gets the list of element IDs that currently describe this control. */\n  describedByIds;\n  static ɵfac = function MatFormFieldControl_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFormFieldControl)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatFormFieldControl\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldControl, [{\n    type: Directive\n  }], null, null);\n})();\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nclass MatFormField {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _dir = inject(Directionality);\n  _platform = inject(Platform);\n  _idGenerator = inject(_IdGenerator);\n  _ngZone = inject(NgZone);\n  _defaults = inject(MAT_FORM_FIELD_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _textField;\n  _iconPrefixContainer;\n  _textPrefixContainer;\n  _iconSuffixContainer;\n  _textSuffixContainer;\n  _floatingLabel;\n  _notchedOutline;\n  _lineRipple;\n  _iconPrefixContainerSignal = viewChild('iconPrefixContainer');\n  _textPrefixContainerSignal = viewChild('textPrefixContainer');\n  _iconSuffixContainerSignal = viewChild('iconSuffixContainer');\n  _textSuffixContainerSignal = viewChild('textSuffixContainer');\n  _prefixSuffixContainers = computed(() => {\n    return [this._iconPrefixContainerSignal(), this._textPrefixContainerSignal(), this._iconSuffixContainerSignal(), this._textSuffixContainerSignal()].map(container => container?.nativeElement).filter(e => e !== undefined);\n  });\n  _formFieldControl;\n  _prefixChildren;\n  _suffixChildren;\n  _errorChildren;\n  _hintChildren;\n  _labelChild = contentChild(MatLabel);\n  /** Whether the required marker should be hidden. */\n  get hideRequiredMarker() {\n    return this._hideRequiredMarker;\n  }\n  set hideRequiredMarker(value) {\n    this._hideRequiredMarker = coerceBooleanProperty(value);\n  }\n  _hideRequiredMarker = false;\n  /**\n   * Theme color of the form field. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color = 'primary';\n  /** Whether the label should always float or float as the user types. */\n  get floatLabel() {\n    return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n  }\n  set floatLabel(value) {\n    if (value !== this._floatLabel) {\n      this._floatLabel = value;\n      // For backwards compatibility. Custom form field controls or directives might set\n      // the \"floatLabel\" input and expect the form field view to be updated automatically.\n      // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n      // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _floatLabel;\n  /** The form field appearance style. */\n  get appearance() {\n    return this._appearanceSignal();\n  }\n  set appearance(value) {\n    const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n        throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n      }\n    }\n    this._appearanceSignal.set(newAppearance);\n  }\n  _appearanceSignal = signal(DEFAULT_APPEARANCE);\n  /**\n   * Whether the form field should reserve space for one line of hint/error text (default)\n   * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n   * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n   */\n  get subscriptSizing() {\n    return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  set subscriptSizing(value) {\n    this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  _subscriptSizing = null;\n  /** Text for the form field hint. */\n  get hintLabel() {\n    return this._hintLabel;\n  }\n  set hintLabel(value) {\n    this._hintLabel = value;\n    this._processHints();\n  }\n  _hintLabel = '';\n  _hasIconPrefix = false;\n  _hasTextPrefix = false;\n  _hasIconSuffix = false;\n  _hasTextSuffix = false;\n  // Unique id for the internal form field label.\n  _labelId = this._idGenerator.getId('mat-mdc-form-field-label-');\n  // Unique id for the hint label.\n  _hintLabelId = this._idGenerator.getId('mat-mdc-hint-');\n  // Ids obtained from the error and hint fields\n  _describedByIds;\n  /** Gets the current form field control */\n  get _control() {\n    return this._explicitFormFieldControl || this._formFieldControl;\n  }\n  set _control(value) {\n    this._explicitFormFieldControl = value;\n  }\n  _destroyed = new Subject();\n  _isFocused = null;\n  _explicitFormFieldControl;\n  _previousControl = null;\n  _previousControlValidatorFn = null;\n  _stateChanges;\n  _valueChanges;\n  _describedByChanges;\n  _animationsDisabled = _animationsDisabled();\n  constructor() {\n    const defaults = this._defaults;\n    if (defaults) {\n      if (defaults.appearance) {\n        this.appearance = defaults.appearance;\n      }\n      this._hideRequiredMarker = Boolean(defaults?.hideRequiredMarker);\n      if (defaults.color) {\n        this.color = defaults.color;\n      }\n    }\n    this._syncOutlineLabelOffset();\n  }\n  ngAfterViewInit() {\n    // Initial focus state sync. This happens rarely, but we want to account for\n    // it in case the form field control has \"focused\" set to true on init.\n    this._updateFocusState();\n    if (!this._animationsDisabled) {\n      this._ngZone.runOutsideAngular(() => {\n        // Enable animations after a certain amount of time so that they don't run on init.\n        setTimeout(() => {\n          this._elementRef.nativeElement.classList.add('mat-form-field-animations-enabled');\n        }, 300);\n      });\n    }\n    // Because the above changes a value used in the template after it was checked, we need\n    // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n    this._changeDetectorRef.detectChanges();\n  }\n  ngAfterContentInit() {\n    this._assertFormFieldControl();\n    this._initializeSubscript();\n    this._initializePrefixAndSuffix();\n  }\n  ngAfterContentChecked() {\n    this._assertFormFieldControl();\n    // if form field was being used with an input in first place and then replaced by other\n    // component such as select.\n    if (this._control !== this._previousControl) {\n      this._initializeControl(this._previousControl);\n      // keep a reference for last validator we had.\n      if (this._control.ngControl && this._control.ngControl.control) {\n        this._previousControlValidatorFn = this._control.ngControl.control.validator;\n      }\n      this._previousControl = this._control;\n    }\n    // make sure the the control has been initialized.\n    if (this._control.ngControl && this._control.ngControl.control) {\n      // get the validators for current control.\n      const validatorFn = this._control.ngControl.control.validator;\n      // if our current validatorFn isn't equal to it might be we are CD behind, marking the\n      // component will allow us to catch up.\n      if (validatorFn !== this._previousControlValidatorFn) {\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._outlineLabelOffsetResizeObserver?.disconnect();\n    this._stateChanges?.unsubscribe();\n    this._valueChanges?.unsubscribe();\n    this._describedByChanges?.unsubscribe();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Gets the id of the label element. If no label is present, returns `null`.\n   */\n  getLabelId = computed(() => this._hasFloatingLabel() ? this._labelId : null);\n  /**\n   * Gets an ElementRef for the element that a overlay attached to the form field\n   * should be positioned relative to.\n   */\n  getConnectedOverlayOrigin() {\n    return this._textField || this._elementRef;\n  }\n  /** Animates the placeholder up and locks it in position. */\n  _animateAndLockLabel() {\n    // This is for backwards compatibility only. Consumers of the form field might use\n    // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n    // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n    // animation. This is different in MDC where the label always animates, so this method\n    // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n    // the floating label state without animations. The non-MDC implementation was inconsistent\n    // because it always animates if \"floatLabel\" is set away from \"always\".\n    // TODO(devversion): consider removing this method when releasing the MDC form field.\n    if (this._hasFloatingLabel()) {\n      this.floatLabel = 'always';\n    }\n  }\n  /** Initializes the registered form field control. */\n  _initializeControl(previousControl) {\n    const control = this._control;\n    const classPrefix = 'mat-mdc-form-field-type-';\n    if (previousControl) {\n      this._elementRef.nativeElement.classList.remove(classPrefix + previousControl.controlType);\n    }\n    if (control.controlType) {\n      this._elementRef.nativeElement.classList.add(classPrefix + control.controlType);\n    }\n    // Subscribe to changes in the child control state in order to update the form field UI.\n    this._stateChanges?.unsubscribe();\n    this._stateChanges = control.stateChanges.subscribe(() => {\n      this._updateFocusState();\n      this._changeDetectorRef.markForCheck();\n    });\n    // Updating the `aria-describedby` touches the DOM. Only do it if it actually needs to change.\n    this._describedByChanges?.unsubscribe();\n    this._describedByChanges = control.stateChanges.pipe(startWith([undefined, undefined]), map(() => [control.errorState, control.userAriaDescribedBy]), pairwise(), filter(([[prevErrorState, prevDescribedBy], [currentErrorState, currentDescribedBy]]) => {\n      return prevErrorState !== currentErrorState || prevDescribedBy !== currentDescribedBy;\n    })).subscribe(() => this._syncDescribedByIds());\n    this._valueChanges?.unsubscribe();\n    // Run change detection if the value changes.\n    if (control.ngControl && control.ngControl.valueChanges) {\n      this._valueChanges = control.ngControl.valueChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n  }\n  _checkPrefixAndSuffixTypes() {\n    this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n    this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n    this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n    this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n  }\n  /** Initializes the prefix and suffix containers. */\n  _initializePrefixAndSuffix() {\n    this._checkPrefixAndSuffixTypes();\n    // Mark the form field as dirty whenever the prefix or suffix children change. This\n    // is necessary because we conditionally display the prefix/suffix containers based\n    // on whether there is projected content.\n    merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n      this._checkPrefixAndSuffixTypes();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /**\n   * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n   * with the custom form field control. Also subscribes to hint and error changes in order\n   * to be able to validate and synchronize ids on change.\n   */\n  _initializeSubscript() {\n    // Re-validate when the number of hints changes.\n    this._hintChildren.changes.subscribe(() => {\n      this._processHints();\n      this._changeDetectorRef.markForCheck();\n    });\n    // Update the aria-described by when the number of errors changes.\n    this._errorChildren.changes.subscribe(() => {\n      this._syncDescribedByIds();\n      this._changeDetectorRef.markForCheck();\n    });\n    // Initial mat-hint validation and subscript describedByIds sync.\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n  /** Throws an error if the form field's control is missing. */\n  _assertFormFieldControl() {\n    if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatFormFieldMissingControlError();\n    }\n  }\n  _updateFocusState() {\n    // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n    // certain DOM events are emitted. This is not possible in our implementation of the\n    // form field because we support abstract form field controls which are not necessarily\n    // of type input, nor do we have a reference to a native form field control element. Instead\n    // we handle the focus by checking if the abstract form field control focused state changes.\n    if (this._control.focused && !this._isFocused) {\n      this._isFocused = true;\n      this._lineRipple?.activate();\n    } else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n      this._isFocused = false;\n      this._lineRipple?.deactivate();\n    }\n    this._textField?.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n  }\n  _outlineLabelOffsetResizeObserver = null;\n  /**\n   * The floating label in the docked state needs to account for prefixes. The horizontal offset\n   * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n   * form field is added to the DOM. This method sets up all subscriptions which are needed to\n   * trigger the label offset update.\n   */\n  _syncOutlineLabelOffset() {\n    afterRenderEffect({\n      earlyRead: () => {\n        if (this._appearanceSignal() !== 'outline') {\n          this._outlineLabelOffsetResizeObserver?.disconnect();\n          return null;\n        }\n        // Setup a resize observer to monitor changes to the size of the prefix / suffix and\n        // readjust the label offset.\n        if (globalThis.ResizeObserver) {\n          this._outlineLabelOffsetResizeObserver ||= new globalThis.ResizeObserver(() => {\n            this._writeOutlinedLabelStyles(this._getOutlinedLabelOffset());\n          });\n          for (const el of this._prefixSuffixContainers()) {\n            this._outlineLabelOffsetResizeObserver.observe(el, {\n              box: 'border-box'\n            });\n          }\n        }\n        return this._getOutlinedLabelOffset();\n      },\n      write: labelStyles => this._writeOutlinedLabelStyles(labelStyles())\n    });\n  }\n  /** Whether the floating label should always float or not. */\n  _shouldAlwaysFloat() {\n    return this.floatLabel === 'always';\n  }\n  _hasOutline() {\n    return this.appearance === 'outline';\n  }\n  /**\n   * Whether the label should display in the infix. Labels in the outline appearance are\n   * displayed as part of the notched-outline and are horizontally offset to account for\n   * form field prefix content. This won't work in server side rendering since we cannot\n   * measure the width of the prefix container. To make the docked label appear as if the\n   * right offset has been calculated, we forcibly render the label inside the infix. Since\n   * the label is part of the infix, the label cannot overflow the prefix content.\n   */\n  _forceDisplayInfixLabel() {\n    return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n  }\n  _hasFloatingLabel = computed(() => !!this._labelChild());\n  _shouldLabelFloat() {\n    if (!this._hasFloatingLabel()) {\n      return false;\n    }\n    return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n  }\n  /**\n   * Determines whether a class from the AbstractControlDirective\n   * should be forwarded to the host element.\n   */\n  _shouldForward(prop) {\n    const control = this._control ? this._control.ngControl : null;\n    return control && control[prop];\n  }\n  /** Gets the type of subscript message to render (error or hint). */\n  _getSubscriptMessageType() {\n    return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState ? 'error' : 'hint';\n  }\n  /** Handle label resize events. */\n  _handleLabelResized() {\n    this._refreshOutlineNotchWidth();\n  }\n  /** Refreshes the width of the outline-notch, if present. */\n  _refreshOutlineNotchWidth() {\n    if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n      this._notchedOutline?._setNotchWidth(0);\n    } else {\n      this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n    }\n  }\n  /** Does any extra processing that is required when handling the hints. */\n  _processHints() {\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n  /**\n   * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n   * label specified set through the input is being considered as \"start\" aligned.\n   *\n   * This method is a noop if Angular runs in production mode.\n   */\n  _validateHints() {\n    if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      let startHint;\n      let endHint;\n      this._hintChildren.forEach(hint => {\n        if (hint.align === 'start') {\n          if (startHint || this.hintLabel) {\n            throw getMatFormFieldDuplicatedHintError('start');\n          }\n          startHint = hint;\n        } else if (hint.align === 'end') {\n          if (endHint) {\n            throw getMatFormFieldDuplicatedHintError('end');\n          }\n          endHint = hint;\n        }\n      });\n    }\n  }\n  /**\n   * Sets the list of element IDs that describe the child control. This allows the control to update\n   * its `aria-describedby` attribute accordingly.\n   */\n  _syncDescribedByIds() {\n    if (this._control) {\n      let ids = [];\n      // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n      if (this._control.userAriaDescribedBy && typeof this._control.userAriaDescribedBy === 'string') {\n        ids.push(...this._control.userAriaDescribedBy.split(' '));\n      }\n      if (this._getSubscriptMessageType() === 'hint') {\n        const startHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'start') : null;\n        const endHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'end') : null;\n        if (startHint) {\n          ids.push(startHint.id);\n        } else if (this._hintLabel) {\n          ids.push(this._hintLabelId);\n        }\n        if (endHint) {\n          ids.push(endHint.id);\n        }\n      } else if (this._errorChildren) {\n        ids.push(...this._errorChildren.map(error => error.id));\n      }\n      const existingDescribedBy = this._control.describedByIds;\n      let toAssign;\n      // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n      // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n      // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n      // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n      if (existingDescribedBy) {\n        const exclude = this._describedByIds || ids;\n        toAssign = ids.concat(existingDescribedBy.filter(id => id && !exclude.includes(id)));\n      } else {\n        toAssign = ids;\n      }\n      this._control.setDescribedByIds(toAssign);\n      this._describedByIds = ids;\n    }\n  }\n  /**\n   * Calculates the horizontal offset of the label in the outline appearance. In the outline\n   * appearance, the notched-outline and label are not relative to the infix container because\n   * the outline intends to surround prefixes, suffixes and the infix. This means that the\n   * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n   * horizontally offset the label by the width of the prefix container. The MDC text-field does\n   * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n   * incorporate the horizontal offset into their default text-field styles.\n   */\n  _getOutlinedLabelOffset() {\n    const dir = this._dir.valueSignal();\n    if (!this._hasOutline() || !this._floatingLabel) {\n      return null;\n    }\n    // If no prefix is displayed, reset the outline label offset from potential\n    // previous label offset updates.\n    if (!this._iconPrefixContainer && !this._textPrefixContainer) {\n      return ['', null];\n    }\n    // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n    // the label offset update until the zone stabilizes.\n    if (!this._isAttachedToDom()) {\n      return null;\n    }\n    const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n    const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n    const iconSuffixContainer = this._iconSuffixContainer?.nativeElement;\n    const textSuffixContainer = this._textSuffixContainer?.nativeElement;\n    const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n    const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n    const iconSuffixContainerWidth = iconSuffixContainer?.getBoundingClientRect().width ?? 0;\n    const textSuffixContainerWidth = textSuffixContainer?.getBoundingClientRect().width ?? 0;\n    // If the directionality is RTL, the x-axis transform needs to be inverted. This\n    // is because `transformX` does not change based on the page directionality.\n    const negate = dir === 'rtl' ? '-1' : '1';\n    const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n    const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n    const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n    // Update the translateX of the floating label to account for the prefix container,\n    // but allow the CSS to override this setting via a CSS variable when the label is\n    // floating.\n    const floatingLabelTransform = 'var(--mat-mdc-form-field-label-transform, ' + `${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset}))`;\n    // Prevent the label from overlapping the suffix when in resting position.\n    const notchedOutlineWidth = iconPrefixContainerWidth + textPrefixContainerWidth + iconSuffixContainerWidth + textSuffixContainerWidth;\n    return [floatingLabelTransform, notchedOutlineWidth];\n  }\n  /** Writes the styles produced by `_getOutlineLabelOffset` synchronously to the DOM. */\n  _writeOutlinedLabelStyles(styles) {\n    if (styles !== null) {\n      const [floatingLabelTransform, notchedOutlineWidth] = styles;\n      if (this._floatingLabel) {\n        this._floatingLabel.element.style.transform = floatingLabelTransform;\n      }\n      if (notchedOutlineWidth !== null) {\n        this._notchedOutline?._setMaxWidth(notchedOutlineWidth);\n      }\n    }\n  }\n  /** Checks whether the form field is attached to the DOM. */\n  _isAttachedToDom() {\n    const element = this._elementRef.nativeElement;\n    if (element.getRootNode) {\n      const rootNode = element.getRootNode();\n      // If the element is inside the DOM the root node will be either the document\n      // or the closest shadow root, otherwise it'll be the element itself.\n      return rootNode && rootNode !== element;\n    }\n    // Otherwise fall back to checking if it's in the document. This doesn't account for\n    // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n    return document.documentElement.contains(element);\n  }\n  static ɵfac = function MatFormField_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatFormField)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatFormField,\n    selectors: [[\"mat-form-field\"]],\n    contentQueries: function MatFormField_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuerySignal(dirIndex, ctx._labelChild, MatLabel, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatFormFieldControl, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_PREFIX, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_SUFFIX, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_ERROR, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatHint, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance();\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._formFieldControl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._prefixChildren = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._suffixChildren = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._errorChildren = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._hintChildren = _t);\n      }\n    },\n    viewQuery: function MatFormField_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuerySignal(ctx._iconPrefixContainerSignal, _c3, 5);\n        i0.ɵɵviewQuerySignal(ctx._textPrefixContainerSignal, _c4, 5);\n        i0.ɵɵviewQuerySignal(ctx._iconSuffixContainerSignal, _c5, 5);\n        i0.ɵɵviewQuerySignal(ctx._textSuffixContainerSignal, _c6, 5);\n        i0.ɵɵviewQuery(_c7, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n        i0.ɵɵviewQuery(MatFormFieldFloatingLabel, 5);\n        i0.ɵɵviewQuery(MatFormFieldNotchedOutline, 5);\n        i0.ɵɵviewQuery(MatFormFieldLineRipple, 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵqueryAdvance(4);\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textField = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconPrefixContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textPrefixContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconSuffixContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textSuffixContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._floatingLabel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notchedOutline = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lineRipple = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-form-field\"],\n    hostVars: 40,\n    hostBindings: function MatFormField_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-form-field-label-always-float\", ctx._shouldAlwaysFloat())(\"mat-mdc-form-field-has-icon-prefix\", ctx._hasIconPrefix)(\"mat-mdc-form-field-has-icon-suffix\", ctx._hasIconSuffix)(\"mat-form-field-invalid\", ctx._control.errorState)(\"mat-form-field-disabled\", ctx._control.disabled)(\"mat-form-field-autofilled\", ctx._control.autofilled)(\"mat-form-field-appearance-fill\", ctx.appearance == \"fill\")(\"mat-form-field-appearance-outline\", ctx.appearance == \"outline\")(\"mat-form-field-hide-placeholder\", ctx._hasFloatingLabel() && !ctx._shouldLabelFloat())(\"mat-focused\", ctx._control.focused)(\"mat-primary\", ctx.color !== \"accent\" && ctx.color !== \"warn\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"ng-untouched\", ctx._shouldForward(\"untouched\"))(\"ng-touched\", ctx._shouldForward(\"touched\"))(\"ng-pristine\", ctx._shouldForward(\"pristine\"))(\"ng-dirty\", ctx._shouldForward(\"dirty\"))(\"ng-valid\", ctx._shouldForward(\"valid\"))(\"ng-invalid\", ctx._shouldForward(\"invalid\"))(\"ng-pending\", ctx._shouldForward(\"pending\"));\n      }\n    },\n    inputs: {\n      hideRequiredMarker: \"hideRequiredMarker\",\n      color: \"color\",\n      floatLabel: \"floatLabel\",\n      appearance: \"appearance\",\n      subscriptSizing: \"subscriptSizing\",\n      hintLabel: \"hintLabel\"\n    },\n    exportAs: [\"matFormField\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_FORM_FIELD,\n      useExisting: MatFormField\n    }, {\n      provide: FLOATING_LABEL_PARENT,\n      useExisting: MatFormField\n    }])],\n    ngContentSelectors: _c9,\n    decls: 19,\n    vars: 25,\n    consts: [[\"labelTemplate\", \"\"], [\"textField\", \"\"], [\"iconPrefixContainer\", \"\"], [\"textPrefixContainer\", \"\"], [\"textSuffixContainer\", \"\"], [\"iconSuffixContainer\", \"\"], [1, \"mat-mdc-text-field-wrapper\", \"mdc-text-field\", 3, \"click\"], [1, \"mat-mdc-form-field-focus-overlay\"], [1, \"mat-mdc-form-field-flex\"], [\"matFormFieldNotchedOutline\", \"\", 3, \"matFormFieldNotchedOutlineOpen\"], [1, \"mat-mdc-form-field-icon-prefix\"], [1, \"mat-mdc-form-field-text-prefix\"], [1, \"mat-mdc-form-field-infix\"], [3, \"ngTemplateOutlet\"], [1, \"mat-mdc-form-field-text-suffix\"], [1, \"mat-mdc-form-field-icon-suffix\"], [\"matFormFieldLineRipple\", \"\"], [1, \"mat-mdc-form-field-subscript-wrapper\", \"mat-mdc-form-field-bottom-align\"], [\"aria-atomic\", \"true\", \"aria-live\", \"polite\"], [\"matFormFieldFloatingLabel\", \"\", 3, \"floating\", \"monitorResize\", \"id\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-form-field-required-marker\", \"mdc-floating-label--required\"], [3, \"id\"], [1, \"mat-mdc-form-field-hint-spacer\"]],\n    template: function MatFormField_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c8);\n        i0.ɵɵtemplate(0, MatFormField_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(2, \"div\", 6, 1);\n        i0.ɵɵlistener(\"click\", function MatFormField_Template_div_click_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._control.onContainerClick($event));\n        });\n        i0.ɵɵconditionalCreate(4, MatFormField_Conditional_4_Template, 1, 0, \"div\", 7);\n        i0.ɵɵelementStart(5, \"div\", 8);\n        i0.ɵɵconditionalCreate(6, MatFormField_Conditional_6_Template, 2, 2, \"div\", 9);\n        i0.ɵɵconditionalCreate(7, MatFormField_Conditional_7_Template, 3, 0, \"div\", 10);\n        i0.ɵɵconditionalCreate(8, MatFormField_Conditional_8_Template, 3, 0, \"div\", 11);\n        i0.ɵɵelementStart(9, \"div\", 12);\n        i0.ɵɵconditionalCreate(10, MatFormField_Conditional_10_Template, 1, 1, null, 13);\n        i0.ɵɵprojection(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵconditionalCreate(12, MatFormField_Conditional_12_Template, 3, 0, \"div\", 14);\n        i0.ɵɵconditionalCreate(13, MatFormField_Conditional_13_Template, 3, 0, \"div\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵconditionalCreate(14, MatFormField_Conditional_14_Template, 1, 0, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"div\", 17)(16, \"div\", 18);\n        i0.ɵɵconditionalCreate(17, MatFormField_Case_17_Template, 1, 0)(18, MatFormField_Case_18_Template, 4, 1);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        let tmp_19_0;\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"mdc-text-field--filled\", !ctx._hasOutline())(\"mdc-text-field--outlined\", ctx._hasOutline())(\"mdc-text-field--no-label\", !ctx._hasFloatingLabel())(\"mdc-text-field--disabled\", ctx._control.disabled)(\"mdc-text-field--invalid\", ctx._control.errorState);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx._hasOutline() && !ctx._control.disabled ? 4 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._hasOutline() ? 6 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._hasIconPrefix ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._hasTextPrefix ? 8 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx._hasOutline() || ctx._forceDisplayInfixLabel() ? 10 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._hasTextSuffix ? 12 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._hasIconSuffix ? 13 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(!ctx._hasOutline() ? 14 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"mat-mdc-form-field-subscript-dynamic-size\", ctx.subscriptSizing === \"dynamic\");\n        const subscriptMessageType_r4 = ctx._getSubscriptMessageType();\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"mat-mdc-form-field-error-wrapper\", subscriptMessageType_r4 === \"error\")(\"mat-mdc-form-field-hint-wrapper\", subscriptMessageType_r4 === \"hint\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional((tmp_19_0 = subscriptMessageType_r4) === \"error\" ? 17 : tmp_19_0 === \"hint\" ? 18 : -1);\n      }\n    },\n    dependencies: [MatFormFieldFloatingLabel, MatFormFieldNotchedOutline, NgTemplateOutlet, MatFormFieldLineRipple, MatHint],\n    styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-filled-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-filled-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-outlined-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-outlined-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-filled-error-caret-color, var(--mat-sys-error))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-outlined-error-caret-color, var(--mat-sys-error))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-filled-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-outlined-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mat-form-field-filled-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mat-form-field-filled-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-filled-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-filled-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-filled-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-filled-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-filled-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mat-form-field-filled-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-filled-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-filled-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-filled-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-outlined-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-outlined-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-outlined-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-outlined-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-outlined-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mat-form-field-outlined-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-outlined-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-outlined-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-outlined-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-outline-color, var(--mat-sys-outline));border-width:var(--mat-form-field-outlined-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mat-form-field-outlined-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{max-width:min(100%,calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mat-form-field-filled-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mat-form-field-filled-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormField, [{\n    type: Component,\n    args: [{\n      selector: 'mat-form-field',\n      exportAs: 'matFormField',\n      host: {\n        'class': 'mat-mdc-form-field',\n        '[class.mat-mdc-form-field-label-always-float]': '_shouldAlwaysFloat()',\n        '[class.mat-mdc-form-field-has-icon-prefix]': '_hasIconPrefix',\n        '[class.mat-mdc-form-field-has-icon-suffix]': '_hasIconSuffix',\n        // Note that these classes reuse the same names as the non-MDC version, because they can be\n        // considered a public API since custom form controls may use them to style themselves.\n        // See https://github.com/angular/components/pull/20502#discussion_r486124901.\n        '[class.mat-form-field-invalid]': '_control.errorState',\n        '[class.mat-form-field-disabled]': '_control.disabled',\n        '[class.mat-form-field-autofilled]': '_control.autofilled',\n        '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n        '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n        '[class.mat-form-field-hide-placeholder]': '_hasFloatingLabel() && !_shouldLabelFloat()',\n        '[class.mat-focused]': '_control.focused',\n        '[class.mat-primary]': 'color !== \"accent\" && color !== \"warn\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n        '[class.ng-touched]': '_shouldForward(\"touched\")',\n        '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n        '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n        '[class.ng-valid]': '_shouldForward(\"valid\")',\n        '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n        '[class.ng-pending]': '_shouldForward(\"pending\")'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MAT_FORM_FIELD,\n        useExisting: MatFormField\n      }, {\n        provide: FLOATING_LABEL_PARENT,\n        useExisting: MatFormField\n      }],\n      imports: [MatFormFieldFloatingLabel, MatFormFieldNotchedOutline, NgTemplateOutlet, MatFormFieldLineRipple, MatHint],\n      template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label\\n      matFormFieldFloatingLabel\\n      [floating]=\\\"_shouldLabelFloat()\\\"\\n      [monitorResize]=\\\"_hasOutline()\\\"\\n      [id]=\\\"_labelId\\\"\\n      [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\"\\n    >\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n      @if (!hideRequiredMarker && _control.required) {\\n        <span\\n          aria-hidden=\\\"true\\\"\\n          class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"\\n        ></span>\\n      }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div\\n  class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\"\\n  #textField\\n  [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n  [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n  [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n  [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n  [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n  (click)=\\\"_control.onContainerClick($event)\\\"\\n>\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\" #textSuffixContainer>\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\" #iconSuffixContainer>\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div\\n    class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n    [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\"\\n>\\n  @let subscriptMessageType = _getSubscriptMessageType();\\n\\n  <!-- \\n    Use a single permanent wrapper for both hints and errors so aria-live works correctly,\\n    as having it appear post render will not consistently work. We also do not want to add\\n    additional divs as it causes styling regressions.\\n    -->\\n  <div aria-atomic=\\\"true\\\" aria-live=\\\"polite\\\" \\n      [class.mat-mdc-form-field-error-wrapper]=\\\"subscriptMessageType === 'error'\\\"\\n      [class.mat-mdc-form-field-hint-wrapper]=\\\"subscriptMessageType === 'hint'\\\"\\n    >\\n    @switch (subscriptMessageType) {\\n      @case ('error') {\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      }\\n\\n      @case ('hint') {\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      }\\n    }\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-filled-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-filled-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-outlined-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-outlined-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-filled-error-caret-color, var(--mat-sys-error))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-outlined-error-caret-color, var(--mat-sys-error))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-filled-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-outlined-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mat-form-field-filled-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mat-form-field-filled-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-filled-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-filled-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-filled-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-filled-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-filled-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mat-form-field-filled-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-filled-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-filled-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-filled-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-outlined-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-outlined-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-outlined-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-outlined-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-outlined-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mat-form-field-outlined-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-outlined-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-outlined-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-outlined-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-outline-color, var(--mat-sys-outline));border-width:var(--mat-form-field-outlined-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mat-form-field-outlined-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{max-width:min(100%,calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mat-form-field-filled-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mat-form-field-filled-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"]\n    }]\n  }], () => [], {\n    _textField: [{\n      type: ViewChild,\n      args: ['textField']\n    }],\n    _iconPrefixContainer: [{\n      type: ViewChild,\n      args: ['iconPrefixContainer']\n    }],\n    _textPrefixContainer: [{\n      type: ViewChild,\n      args: ['textPrefixContainer']\n    }],\n    _iconSuffixContainer: [{\n      type: ViewChild,\n      args: ['iconSuffixContainer']\n    }],\n    _textSuffixContainer: [{\n      type: ViewChild,\n      args: ['textSuffixContainer']\n    }],\n    _floatingLabel: [{\n      type: ViewChild,\n      args: [MatFormFieldFloatingLabel]\n    }],\n    _notchedOutline: [{\n      type: ViewChild,\n      args: [MatFormFieldNotchedOutline]\n    }],\n    _lineRipple: [{\n      type: ViewChild,\n      args: [MatFormFieldLineRipple]\n    }],\n    _formFieldControl: [{\n      type: ContentChild,\n      args: [MatFormFieldControl]\n    }],\n    _prefixChildren: [{\n      type: ContentChildren,\n      args: [MAT_PREFIX, {\n        descendants: true\n      }]\n    }],\n    _suffixChildren: [{\n      type: ContentChildren,\n      args: [MAT_SUFFIX, {\n        descendants: true\n      }]\n    }],\n    _errorChildren: [{\n      type: ContentChildren,\n      args: [MAT_ERROR, {\n        descendants: true\n      }]\n    }],\n    _hintChildren: [{\n      type: ContentChildren,\n      args: [MatHint, {\n        descendants: true\n      }]\n    }],\n    hideRequiredMarker: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    floatLabel: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }],\n    subscriptSizing: [{\n      type: Input\n    }],\n    hintLabel: [{\n      type: Input\n    }]\n  });\n})();\nexport { MatLabel as M, MAT_ERROR as a, MatError as b, MatHint as c, MAT_PREFIX as d, MatPrefix as e, MAT_SUFFIX as f, MatSuffix as g, MAT_FORM_FIELD as h, MAT_FORM_FIELD_DEFAULT_OPTIONS as i, MatFormField as j, MatFormFieldControl as k, getMatFormFieldPlaceholderConflictError as l, getMatFormFieldDuplicatedHintError as m, getMatFormFieldMissingControlError as n };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,8BAA8B,EAAE;AAC7C,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,qBAAqB;AAClC,IAAM,MAAM,CAAC,qBAAqB;AAClC,IAAM,MAAM,CAAC,qBAAqB;AAClC,IAAM,MAAM,CAAC,qBAAqB;AAClC,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,YAAY,SAAS,KAAK,CAAC,CAAC;AAC7S,IAAM,MAAM,CAAC,KAAK,aAAa,gCAAgC,mBAAmB,mBAAmB,gCAAgC,yBAAyB,+BAA+B,uBAAuB;AACpN,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,oBAAoB,GAAG,iEAAiE,GAAG,GAAG,QAAQ,EAAE;AAC3G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,kBAAkB,CAAC,EAAE,iBAAiB,OAAO,YAAY,CAAC,EAAE,MAAM,OAAO,QAAQ;AAClH,IAAG,YAAY,OAAO,OAAO,SAAS,2BAA2B,OAAO,OAAO,SAAS,EAAE;AAC1F,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,CAAC,OAAO,sBAAsB,OAAO,SAAS,WAAW,IAAI,EAAE;AAAA,EAClF;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,mDAAmD,GAAG,GAAG,SAAS,EAAE;AAAA,EAChG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,kBAAkB,IAAI,IAAI,EAAE;AAAA,EACtD;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAAC;AACnF,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,eAAe,EAAE;AAAA,EAC3G;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,oBAAoB,GAAG,mDAAmD,GAAG,GAAG,MAAM,EAAE;AAC3F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,kCAAkC,OAAO,kBAAkB,CAAC;AAC1E,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,wBAAwB,IAAI,IAAI,EAAE;AAAA,EAC7D;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AAAC;AACtE,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,eAAe,EAAE;AAAA,EAC9F;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,MAAM,OAAO,YAAY;AACvC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,SAAS;AAAA,EACvC;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,oBAAoB,GAAG,6CAA6C,GAAG,GAAG,YAAY,EAAE;AAC3F,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,YAAY,IAAI,EAAE;AAAA,EAC5C;AACF;AACA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,YAAY,IAAI,eAAe,UAAU;AAE/C,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,KAAK,OAAO,YAAY,EAAE,MAAM,gBAAgB;AAAA,EAChD,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,IAC/C,WAAW,CAAC,GAAG,4BAA4B,iCAAiC;AAAA,IAC5E,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,MAAM,IAAI,EAAE;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,IAAI;AAAA,IACN;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA,EAEZ,QAAQ;AAAA;AAAA,EAER,KAAK,OAAO,YAAY,EAAE,MAAM,eAAe;AAAA,EAC/C,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,WAAW,CAAC,GAAG,2BAA2B,iCAAiC;AAAA,IAC3E,UAAU;AAAA,IACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,MAAM,IAAI,EAAE;AAC7B,QAAG,YAAY,SAAS,IAAI;AAC5B,QAAG,YAAY,+BAA+B,IAAI,UAAU,KAAK;AAAA,MACnE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,IAAI;AAAA,IACN;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,uCAAuC;AAAA,QACvC,QAAQ;AAAA;AAAA,QAER,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAa,IAAI,eAAe,WAAW;AAEjD,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,IAAI,gBAAgB,OAAO;AACzB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,EACV,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACvF,QAAQ;AAAA,MACN,iBAAiB,CAAC,GAAG,iBAAiB,iBAAiB;AAAA,IACzD;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAa,IAAI,eAAe,WAAW;AAEjD,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,IAAI,gBAAgB,OAAO;AACzB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,EACV,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACvF,QAAQ;AAAA,MACN,iBAAiB,CAAC,GAAG,iBAAiB,iBAAiB;AAAA,IACzD;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,wBAAwB,IAAI,eAAe,qBAAqB;AActE,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB;AACtB,QAAI,KAAK,gBAAgB;AACvB,WAAK,mBAAmB;AAAA,IAC1B,OAAO;AACL,WAAK,oBAAoB,YAAY;AAAA,IACvC;AAAA,EACF;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,kBAAkB,OAAO,oBAAoB;AAAA;AAAA,EAE7C,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,UAAU,OAAO,qBAAqB;AAAA;AAAA,EAEtC,sBAAsB,IAAI,aAAa;AAAA,EACvC,cAAc;AAAA,EAAC;AAAA,EACf,cAAc;AACZ,SAAK,oBAAoB,YAAY;AAAA,EACvC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,oBAAoB,KAAK,YAAY,aAAa;AAAA,EAC3D;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,gBAAgB;AASd,eAAW,MAAM,KAAK,QAAQ,oBAAoB,CAAC;AAAA,EACrD;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,oBAAoB,YAAY;AACrC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,sBAAsB,KAAK,gBAAgB,QAAQ,KAAK,YAAY,eAAe;AAAA,QACtF,KAAK;AAAA,MACP,CAAC,EAAE,UAAU,MAAM,KAAK,cAAc,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,6BAA6B,EAAE,CAAC;AAAA,IACtD,WAAW,CAAC,GAAG,sBAAsB,wBAAwB;AAAA,IAC7D,UAAU;AAAA,IACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,mCAAmC,IAAI,QAAQ;AAAA,MAChE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,2CAA2C;AAAA,MAC7C;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,SAAS,oBAAoB,SAAS;AAKpC,QAAM,SAAS;AACf,MAAI,OAAO,iBAAiB,MAAM;AAChC,WAAO,OAAO;AAAA,EAChB;AACA,QAAM,QAAQ,OAAO,UAAU,IAAI;AACnC,QAAM,MAAM,YAAY,YAAY,UAAU;AAC9C,QAAM,MAAM,YAAY,aAAa,6BAA6B;AAClE,WAAS,gBAAgB,YAAY,KAAK;AAC1C,QAAM,cAAc,MAAM;AAC1B,QAAM,OAAO;AACb,SAAO;AACT;AAGA,IAAM,iBAAiB;AAEvB,IAAM,qBAAqB;AAS3B,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc,OAAO,UAAU;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAM,WAAW,OAAO,SAAS;AACjC,WAAO,kBAAkB,MAAM;AAC7B,WAAK,wBAAwB,SAAS,OAAO,KAAK,YAAY,eAAe,iBAAiB,KAAK,oBAAoB;AAAA,IACzH,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,YAAY,KAAK,YAAY,cAAc;AACjD,cAAU,OAAO,kBAAkB;AACnC,cAAU,IAAI,cAAc;AAAA,EAC9B;AAAA,EACA,aAAa;AACX,SAAK,YAAY,cAAc,UAAU,IAAI,kBAAkB;AAAA,EACjE;AAAA,EACA,uBAAuB,WAAS;AAC9B,UAAM,YAAY,KAAK,YAAY,cAAc;AACjD,UAAM,iBAAiB,UAAU,SAAS,kBAAkB;AAC5D,QAAI,MAAM,iBAAiB,aAAa,gBAAgB;AACtD,gBAAU,OAAO,gBAAgB,kBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,OAAO,0BAA0B,EAAE,CAAC;AAAA,IACjD,WAAW,CAAC,GAAG,iBAAiB;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAQH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,OAAO;AAAA,EACP;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,QAAQ,QAAQ,cAAc,qBAAqB;AACzD,QAAI,OAAO;AACT,cAAQ,UAAU,IAAI,+BAA+B;AACrD,UAAI,OAAO,0BAA0B,YAAY;AAC/C,cAAM,MAAM,qBAAqB;AACjC,aAAK,QAAQ,kBAAkB,MAAM;AACnC,gCAAsB,MAAM,MAAM,MAAM,qBAAqB,EAAE;AAAA,QACjE,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,cAAQ,UAAU,IAAI,+BAA+B;AAAA,IACvD;AAAA,EACF;AAAA,EACA,eAAe,YAAY;AACzB,UAAM,QAAQ,KAAK,OAAO;AAC1B,QAAI,CAAC,KAAK,QAAQ,CAAC,YAAY;AAC7B,YAAM,MAAM,QAAQ;AAAA,IACtB,OAAO;AACL,YAAM,wBAAwB;AAC9B,YAAM,uBAAuB;AAC7B,YAAM,MAAM,QAAQ,QAAQ,UAAU,+DAA+D,wBAAwB,oBAAoB;AAAA,IACnJ;AAAA,EACF;AAAA,EACA,aAAa,sBAAsB;AAEjC,SAAK,OAAO,cAAc,MAAM,YAAY,oCAAoC,eAAe,oBAAoB,KAAK;AAAA,EAC1H;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,OAAO,8BAA8B,EAAE,CAAC;AAAA,IACrD,WAAW,SAAS,iCAAiC,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,UAAU;AAAA,IACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gCAAgC,IAAI,IAAI;AAAA,MACzD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,kCAAkC,MAAM;AAAA,IACpD;AAAA,IACA,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,uBAAuB,8BAA8B,GAAG,CAAC,GAAG,uBAAuB,4BAA4B,GAAG,CAAC,GAAG,uBAAuB,+BAA+B,CAAC;AAAA,IACzM,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAChB,QAAG,UAAU,GAAG,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA,QAGT,wCAAwC;AAAA,MAC1C;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,EACR,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,SAAS,0CAA0C;AACjD,SAAO,MAAM,8DAA8D;AAC7E;AAEA,SAAS,mCAAmC,OAAO;AACjD,SAAO,MAAM,2CAA2C,KAAK,KAAK;AACpE;AAEA,SAAS,qCAAqC;AAC5C,SAAO,MAAM,oDAAoD;AACnE;AAOA,IAAM,iBAAiB,IAAI,eAAe,cAAc;AAKxD,IAAM,iCAAiC,IAAI,eAAe,gCAAgC;AAE1F,IAAM,qBAAqB;AAK3B,IAAM,sBAAsB;AAE5B,IAAM,2BAA2B;AAMjC,IAAM,0CAA0C;AAEhD,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,OAAO,OAAO,cAAc;AAAA,EAC5B,YAAY,OAAO,QAAQ;AAAA,EAC3B,eAAe,OAAO,YAAY;AAAA,EAClC,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,gCAAgC;AAAA,IACjD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,6BAA6B,UAAU,qBAAqB;AAAA,EAC5D,6BAA6B,UAAU,qBAAqB;AAAA,EAC5D,6BAA6B,UAAU,qBAAqB;AAAA,EAC5D,6BAA6B,UAAU,qBAAqB;AAAA,EAC5D,0BAA0B,SAAS,MAAM;AACvC,WAAO,CAAC,KAAK,2BAA2B,GAAG,KAAK,2BAA2B,GAAG,KAAK,2BAA2B,GAAG,KAAK,2BAA2B,CAAC,EAAE,IAAI,eAAa,WAAW,aAAa,EAAE,OAAO,OAAK,MAAM,MAAS;AAAA,EAC5N,CAAC;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc,aAAa,QAAQ;AAAA;AAAA,EAEnC,IAAI,qBAAqB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,mBAAmB,OAAO;AAC5B,SAAK,sBAAsB,sBAAsB,KAAK;AAAA,EACxD;AAAA,EACA,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQtB,QAAQ;AAAA;AAAA,EAER,IAAI,aAAa;AACf,WAAO,KAAK,eAAe,KAAK,WAAW,cAAc;AAAA,EAC3D;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,QAAI,UAAU,KAAK,aAAa;AAC9B,WAAK,cAAc;AAKnB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,kBAAkB;AAAA,EAChC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,UAAM,gBAAgB,SAAS,KAAK,WAAW,cAAc;AAC7D,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,kBAAkB,UAAU,kBAAkB,WAAW;AAC3D,cAAM,IAAI,MAAM,qCAAqC,aAAa,0CAA0C;AAAA,MAC9G;AAAA,IACF;AACA,SAAK,kBAAkB,IAAI,aAAa;AAAA,EAC1C;AAAA,EACA,oBAAoB,OAAO,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7C,IAAI,kBAAkB;AACpB,WAAO,KAAK,oBAAoB,KAAK,WAAW,mBAAmB;AAAA,EACrE;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,SAAK,mBAAmB,SAAS,KAAK,WAAW,mBAAmB;AAAA,EACtE;AAAA,EACA,mBAAmB;AAAA;AAAA,EAEnB,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa;AAClB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA;AAAA,EAEjB,WAAW,KAAK,aAAa,MAAM,2BAA2B;AAAA;AAAA,EAE9D,eAAe,KAAK,aAAa,MAAM,eAAe;AAAA;AAAA,EAEtD;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,6BAA6B,KAAK;AAAA,EAChD;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,4BAA4B;AAAA,EACnC;AAAA,EACA,aAAa,IAAI,QAAQ;AAAA,EACzB,aAAa;AAAA,EACb;AAAA,EACA,mBAAmB;AAAA,EACnB,8BAA8B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB,oBAAoB;AAAA,EAC1C,cAAc;AACZ,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACZ,UAAI,SAAS,YAAY;AACvB,aAAK,aAAa,SAAS;AAAA,MAC7B;AACA,WAAK,sBAAsB,QAAQ,UAAU,kBAAkB;AAC/D,UAAI,SAAS,OAAO;AAClB,aAAK,QAAQ,SAAS;AAAA,MACxB;AAAA,IACF;AACA,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,kBAAkB;AAGhB,SAAK,kBAAkB;AACvB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,QAAQ,kBAAkB,MAAM;AAEnC,mBAAW,MAAM;AACf,eAAK,YAAY,cAAc,UAAU,IAAI,mCAAmC;AAAA,QAClF,GAAG,GAAG;AAAA,MACR,CAAC;AAAA,IACH;AAGA,SAAK,mBAAmB,cAAc;AAAA,EACxC;AAAA,EACA,qBAAqB;AACnB,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,wBAAwB;AACtB,SAAK,wBAAwB;AAG7B,QAAI,KAAK,aAAa,KAAK,kBAAkB;AAC3C,WAAK,mBAAmB,KAAK,gBAAgB;AAE7C,UAAI,KAAK,SAAS,aAAa,KAAK,SAAS,UAAU,SAAS;AAC9D,aAAK,8BAA8B,KAAK,SAAS,UAAU,QAAQ;AAAA,MACrE;AACA,WAAK,mBAAmB,KAAK;AAAA,IAC/B;AAEA,QAAI,KAAK,SAAS,aAAa,KAAK,SAAS,UAAU,SAAS;AAE9D,YAAM,cAAc,KAAK,SAAS,UAAU,QAAQ;AAGpD,UAAI,gBAAgB,KAAK,6BAA6B;AACpD,aAAK,mBAAmB,aAAa;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,mCAAmC,WAAW;AACnD,SAAK,eAAe,YAAY;AAChC,SAAK,eAAe,YAAY;AAChC,SAAK,qBAAqB,YAAY;AACtC,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS,MAAM,KAAK,kBAAkB,IAAI,KAAK,WAAW,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3E,4BAA4B;AAC1B,WAAO,KAAK,cAAc,KAAK;AAAA,EACjC;AAAA;AAAA,EAEA,uBAAuB;AASrB,QAAI,KAAK,kBAAkB,GAAG;AAC5B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,iBAAiB;AAClC,UAAM,UAAU,KAAK;AACrB,UAAM,cAAc;AACpB,QAAI,iBAAiB;AACnB,WAAK,YAAY,cAAc,UAAU,OAAO,cAAc,gBAAgB,WAAW;AAAA,IAC3F;AACA,QAAI,QAAQ,aAAa;AACvB,WAAK,YAAY,cAAc,UAAU,IAAI,cAAc,QAAQ,WAAW;AAAA,IAChF;AAEA,SAAK,eAAe,YAAY;AAChC,SAAK,gBAAgB,QAAQ,aAAa,UAAU,MAAM;AACxD,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAED,SAAK,qBAAqB,YAAY;AACtC,SAAK,sBAAsB,QAAQ,aAAa,KAAK,UAAU,CAAC,QAAW,MAAS,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,YAAY,QAAQ,mBAAmB,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,gBAAgB,eAAe,GAAG,CAAC,mBAAmB,kBAAkB,CAAC,MAAM;AACzP,aAAO,mBAAmB,qBAAqB,oBAAoB;AAAA,IACrE,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,oBAAoB,CAAC;AAC9C,SAAK,eAAe,YAAY;AAEhC,QAAI,QAAQ,aAAa,QAAQ,UAAU,cAAc;AACvD,WAAK,gBAAgB,QAAQ,UAAU,aAAa,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,IAC7I;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,SAAK,iBAAiB,CAAC,CAAC,KAAK,gBAAgB,KAAK,OAAK,CAAC,EAAE,OAAO;AACjE,SAAK,iBAAiB,CAAC,CAAC,KAAK,gBAAgB,KAAK,OAAK,EAAE,OAAO;AAChE,SAAK,iBAAiB,CAAC,CAAC,KAAK,gBAAgB,KAAK,OAAK,CAAC,EAAE,OAAO;AACjE,SAAK,iBAAiB,CAAC,CAAC,KAAK,gBAAgB,KAAK,OAAK,EAAE,OAAO;AAAA,EAClE;AAAA;AAAA,EAEA,6BAA6B;AAC3B,SAAK,2BAA2B;AAIhC,UAAM,KAAK,gBAAgB,SAAS,KAAK,gBAAgB,OAAO,EAAE,UAAU,MAAM;AAChF,WAAK,2BAA2B;AAChC,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AAErB,SAAK,cAAc,QAAQ,UAAU,MAAM;AACzC,WAAK,cAAc;AACnB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAED,SAAK,eAAe,QAAQ,UAAU,MAAM;AAC1C,WAAK,oBAAoB;AACzB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAED,SAAK,eAAe;AACpB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,0BAA0B;AACxB,QAAI,CAAC,KAAK,aAAa,OAAO,cAAc,eAAe,YAAY;AACrE,YAAM,mCAAmC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,oBAAoB;AAMlB,QAAI,KAAK,SAAS,WAAW,CAAC,KAAK,YAAY;AAC7C,WAAK,aAAa;AAClB,WAAK,aAAa,SAAS;AAAA,IAC7B,WAAW,CAAC,KAAK,SAAS,YAAY,KAAK,cAAc,KAAK,eAAe,OAAO;AAClF,WAAK,aAAa;AAClB,WAAK,aAAa,WAAW;AAAA,IAC/B;AACA,SAAK,YAAY,cAAc,UAAU,OAAO,2BAA2B,KAAK,SAAS,OAAO;AAAA,EAClG;AAAA,EACA,oCAAoC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpC,0BAA0B;AACxB,sBAAkB;AAAA,MAChB,WAAW,MAAM;AACf,YAAI,KAAK,kBAAkB,MAAM,WAAW;AAC1C,eAAK,mCAAmC,WAAW;AACnD,iBAAO;AAAA,QACT;AAGA,YAAI,WAAW,gBAAgB;AAC7B,eAAK,sCAAsC,IAAI,WAAW,eAAe,MAAM;AAC7E,iBAAK,0BAA0B,KAAK,wBAAwB,CAAC;AAAA,UAC/D,CAAC;AACD,qBAAW,MAAM,KAAK,wBAAwB,GAAG;AAC/C,iBAAK,kCAAkC,QAAQ,IAAI;AAAA,cACjD,KAAK;AAAA,YACP,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,KAAK,wBAAwB;AAAA,MACtC;AAAA,MACA,OAAO,iBAAe,KAAK,0BAA0B,YAAY,CAAC;AAAA,IACpE,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,0BAA0B;AACxB,WAAO,CAAC,KAAK,UAAU,aAAa,KAAK,gBAAgB,UAAU,CAAC,KAAK,kBAAkB;AAAA,EAC7F;AAAA,EACA,oBAAoB,SAAS,MAAM,CAAC,CAAC,KAAK,YAAY,CAAC;AAAA,EACvD,oBAAoB;AAClB,QAAI,CAAC,KAAK,kBAAkB,GAAG;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,SAAS,oBAAoB,KAAK,mBAAmB;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM;AACnB,UAAM,UAAU,KAAK,WAAW,KAAK,SAAS,YAAY;AAC1D,WAAO,WAAW,QAAQ,IAAI;AAAA,EAChC;AAAA;AAAA,EAEA,2BAA2B;AACzB,WAAO,KAAK,kBAAkB,KAAK,eAAe,SAAS,KAAK,KAAK,SAAS,aAAa,UAAU;AAAA,EACvG;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA,EAEA,4BAA4B;AAC1B,QAAI,CAAC,KAAK,YAAY,KAAK,CAAC,KAAK,kBAAkB,CAAC,KAAK,kBAAkB,GAAG;AAC5E,WAAK,iBAAiB,eAAe,CAAC;AAAA,IACxC,OAAO;AACL,WAAK,iBAAiB,eAAe,KAAK,eAAe,SAAS,CAAC;AAAA,IACrE;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,SAAK,eAAe;AACpB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,kBAAkB,OAAO,cAAc,eAAe,YAAY;AACzE,UAAI;AACJ,UAAI;AACJ,WAAK,cAAc,QAAQ,UAAQ;AACjC,YAAI,KAAK,UAAU,SAAS;AAC1B,cAAI,aAAa,KAAK,WAAW;AAC/B,kBAAM,mCAAmC,OAAO;AAAA,UAClD;AACA,sBAAY;AAAA,QACd,WAAW,KAAK,UAAU,OAAO;AAC/B,cAAI,SAAS;AACX,kBAAM,mCAAmC,KAAK;AAAA,UAChD;AACA,oBAAU;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,QAAI,KAAK,UAAU;AACjB,UAAI,MAAM,CAAC;AAEX,UAAI,KAAK,SAAS,uBAAuB,OAAO,KAAK,SAAS,wBAAwB,UAAU;AAC9F,YAAI,KAAK,GAAG,KAAK,SAAS,oBAAoB,MAAM,GAAG,CAAC;AAAA,MAC1D;AACA,UAAI,KAAK,yBAAyB,MAAM,QAAQ;AAC9C,cAAM,YAAY,KAAK,gBAAgB,KAAK,cAAc,KAAK,UAAQ,KAAK,UAAU,OAAO,IAAI;AACjG,cAAM,UAAU,KAAK,gBAAgB,KAAK,cAAc,KAAK,UAAQ,KAAK,UAAU,KAAK,IAAI;AAC7F,YAAI,WAAW;AACb,cAAI,KAAK,UAAU,EAAE;AAAA,QACvB,WAAW,KAAK,YAAY;AAC1B,cAAI,KAAK,KAAK,YAAY;AAAA,QAC5B;AACA,YAAI,SAAS;AACX,cAAI,KAAK,QAAQ,EAAE;AAAA,QACrB;AAAA,MACF,WAAW,KAAK,gBAAgB;AAC9B,YAAI,KAAK,GAAG,KAAK,eAAe,IAAI,WAAS,MAAM,EAAE,CAAC;AAAA,MACxD;AACA,YAAM,sBAAsB,KAAK,SAAS;AAC1C,UAAI;AAKJ,UAAI,qBAAqB;AACvB,cAAM,UAAU,KAAK,mBAAmB;AACxC,mBAAW,IAAI,OAAO,oBAAoB,OAAO,QAAM,MAAM,CAAC,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,MACrF,OAAO;AACL,mBAAW;AAAA,MACb;AACA,WAAK,SAAS,kBAAkB,QAAQ;AACxC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B;AACxB,UAAM,MAAM,KAAK,KAAK,YAAY;AAClC,QAAI,CAAC,KAAK,YAAY,KAAK,CAAC,KAAK,gBAAgB;AAC/C,aAAO;AAAA,IACT;AAGA,QAAI,CAAC,KAAK,wBAAwB,CAAC,KAAK,sBAAsB;AAC5D,aAAO,CAAC,IAAI,IAAI;AAAA,IAClB;AAGA,QAAI,CAAC,KAAK,iBAAiB,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,sBAAsB,KAAK,sBAAsB;AACvD,UAAM,sBAAsB,KAAK,sBAAsB;AACvD,UAAM,sBAAsB,KAAK,sBAAsB;AACvD,UAAM,sBAAsB,KAAK,sBAAsB;AACvD,UAAM,2BAA2B,qBAAqB,sBAAsB,EAAE,SAAS;AACvF,UAAM,2BAA2B,qBAAqB,sBAAsB,EAAE,SAAS;AACvF,UAAM,2BAA2B,qBAAqB,sBAAsB,EAAE,SAAS;AACvF,UAAM,2BAA2B,qBAAqB,sBAAsB,EAAE,SAAS;AAGvF,UAAM,SAAS,QAAQ,QAAQ,OAAO;AACtC,UAAM,cAAc,GAAG,2BAA2B,wBAAwB;AAC1E,UAAM,cAAc;AACpB,UAAM,wBAAwB,QAAQ,MAAM,OAAO,WAAW,MAAM,WAAW;AAI/E,UAAM,yBAAyB,6CAAkD,uCAAuC,eAAe,qBAAqB;AAE5J,UAAM,sBAAsB,2BAA2B,2BAA2B,2BAA2B;AAC7G,WAAO,CAAC,wBAAwB,mBAAmB;AAAA,EACrD;AAAA;AAAA,EAEA,0BAA0B,QAAQ;AAChC,QAAI,WAAW,MAAM;AACnB,YAAM,CAAC,wBAAwB,mBAAmB,IAAI;AACtD,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,QAAQ,MAAM,YAAY;AAAA,MAChD;AACA,UAAI,wBAAwB,MAAM;AAChC,aAAK,iBAAiB,aAAa,mBAAmB;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,QAAQ,aAAa;AACvB,YAAM,WAAW,QAAQ,YAAY;AAGrC,aAAO,YAAY,aAAa;AAAA,IAClC;AAGA,WAAO,SAAS,gBAAgB,SAAS,OAAO;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,gBAAgB,SAAS,4BAA4B,IAAI,KAAK,UAAU;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,qBAAqB,UAAU,IAAI,aAAa,UAAU,CAAC;AAC9D,QAAG,eAAe,UAAU,qBAAqB,CAAC;AAClD,QAAG,eAAe,UAAU,YAAY,CAAC;AACzC,QAAG,eAAe,UAAU,YAAY,CAAC;AACzC,QAAG,eAAe,UAAU,WAAW,CAAC;AACxC,QAAG,eAAe,UAAU,SAAS,CAAC;AAAA,MACxC;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe;AAClB,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB;AAAA,MACnE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,mBAAmB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,kBAAkB,IAAI,4BAA4B,KAAK,CAAC;AAC3D,QAAG,kBAAkB,IAAI,4BAA4B,KAAK,CAAC;AAC3D,QAAG,kBAAkB,IAAI,4BAA4B,KAAK,CAAC;AAC3D,QAAG,kBAAkB,IAAI,4BAA4B,KAAK,CAAC;AAC3D,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,2BAA2B,CAAC;AAC3C,QAAG,YAAY,4BAA4B,CAAC;AAC5C,QAAG,YAAY,wBAAwB,CAAC;AAAA,MAC1C;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,CAAC;AACnB,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AACtE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,oBAAoB;AAAA,IACnC,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,yCAAyC,IAAI,mBAAmB,CAAC,EAAE,sCAAsC,IAAI,cAAc,EAAE,sCAAsC,IAAI,cAAc,EAAE,0BAA0B,IAAI,SAAS,UAAU,EAAE,2BAA2B,IAAI,SAAS,QAAQ,EAAE,6BAA6B,IAAI,SAAS,UAAU,EAAE,kCAAkC,IAAI,cAAc,MAAM,EAAE,qCAAqC,IAAI,cAAc,SAAS,EAAE,mCAAmC,IAAI,kBAAkB,KAAK,CAAC,IAAI,kBAAkB,CAAC,EAAE,eAAe,IAAI,SAAS,OAAO,EAAE,eAAe,IAAI,UAAU,YAAY,IAAI,UAAU,MAAM,EAAE,cAAc,IAAI,UAAU,QAAQ,EAAE,YAAY,IAAI,UAAU,MAAM,EAAE,gBAAgB,IAAI,eAAe,WAAW,CAAC,EAAE,cAAc,IAAI,eAAe,SAAS,CAAC,EAAE,eAAe,IAAI,eAAe,UAAU,CAAC,EAAE,YAAY,IAAI,eAAe,OAAO,CAAC,EAAE,YAAY,IAAI,eAAe,OAAO,CAAC,EAAE,cAAc,IAAI,eAAe,SAAS,CAAC,EAAE,cAAc,IAAI,eAAe,SAAS,CAAC;AAAA,MAC5hC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,GAAG,8BAA8B,kBAAkB,GAAG,OAAO,GAAG,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,8BAA8B,IAAI,GAAG,gCAAgC,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,0BAA0B,EAAE,GAAG,CAAC,GAAG,wCAAwC,iCAAiC,GAAG,CAAC,eAAe,QAAQ,aAAa,QAAQ,GAAG,CAAC,6BAA6B,IAAI,GAAG,YAAY,iBAAiB,IAAI,GAAG,CAAC,eAAe,QAAQ,GAAG,sCAAsC,8BAA8B,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,gCAAgC,CAAC;AAAA,IAC38B,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC7G,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,SAAS,SAAS,2CAA2C,QAAQ;AACjF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,SAAS,iBAAiB,MAAM,CAAC;AAAA,QAC7D,CAAC;AACD,QAAG,oBAAoB,GAAG,qCAAqC,GAAG,GAAG,OAAO,CAAC;AAC7E,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,oBAAoB,GAAG,qCAAqC,GAAG,GAAG,OAAO,CAAC;AAC7E,QAAG,oBAAoB,GAAG,qCAAqC,GAAG,GAAG,OAAO,EAAE;AAC9E,QAAG,oBAAoB,GAAG,qCAAqC,GAAG,GAAG,OAAO,EAAE;AAC9E,QAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,QAAG,oBAAoB,IAAI,sCAAsC,GAAG,GAAG,MAAM,EAAE;AAC/E,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa;AAChB,QAAG,oBAAoB,IAAI,sCAAsC,GAAG,GAAG,OAAO,EAAE;AAChF,QAAG,oBAAoB,IAAI,sCAAsC,GAAG,GAAG,OAAO,EAAE;AAChF,QAAG,aAAa;AAChB,QAAG,oBAAoB,IAAI,sCAAsC,GAAG,GAAG,OAAO,EAAE;AAChF,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE;AAC9C,QAAG,oBAAoB,IAAI,+BAA+B,GAAG,CAAC,EAAE,IAAI,+BAA+B,GAAG,CAAC;AACvG,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,0BAA0B,CAAC,IAAI,YAAY,CAAC,EAAE,4BAA4B,IAAI,YAAY,CAAC,EAAE,4BAA4B,CAAC,IAAI,kBAAkB,CAAC,EAAE,4BAA4B,IAAI,SAAS,QAAQ,EAAE,2BAA2B,IAAI,SAAS,UAAU;AACvQ,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,YAAY,KAAK,CAAC,IAAI,SAAS,WAAW,IAAI,EAAE;AACtE,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,YAAY,IAAI,IAAI,EAAE;AAC3C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAC5C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAC5C,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,CAAC,IAAI,YAAY,KAAK,IAAI,wBAAwB,IAAI,KAAK,EAAE;AAC9E,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,iBAAiB,KAAK,EAAE;AAC7C,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,iBAAiB,KAAK,EAAE;AAC7C,QAAG,UAAU;AACb,QAAG,cAAc,CAAC,IAAI,YAAY,IAAI,KAAK,EAAE;AAC7C,QAAG,UAAU;AACb,QAAG,YAAY,6CAA6C,IAAI,oBAAoB,SAAS;AAC7F,cAAM,0BAA0B,IAAI,yBAAyB;AAC7D,QAAG,UAAU;AACb,QAAG,YAAY,oCAAoC,4BAA4B,OAAO,EAAE,mCAAmC,4BAA4B,MAAM;AAC7J,QAAG,UAAU;AACb,QAAG,eAAe,WAAW,6BAA6B,UAAU,KAAK,aAAa,SAAS,KAAK,EAAE;AAAA,MACxG;AAAA,IACF;AAAA,IACA,cAAc,CAAC,2BAA2B,4BAA4B,kBAAkB,wBAAwB,OAAO;AAAA,IACvH,QAAQ,CAAC,usiCAA+siC;AAAA,IACxtiC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,iDAAiD;AAAA,QACjD,8CAA8C;AAAA,QAC9C,8CAA8C;AAAA;AAAA;AAAA;AAAA,QAI9C,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,qCAAqC;AAAA,QACrC,0CAA0C;AAAA,QAC1C,6CAA6C;AAAA,QAC7C,2CAA2C;AAAA,QAC3C,uBAAuB;AAAA,QACvB,uBAAuB;AAAA,QACvB,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,sBAAsB;AAAA,MACxB;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,2BAA2B,4BAA4B,kBAAkB,wBAAwB,OAAO;AAAA,MAClH,UAAU;AAAA,MACV,QAAQ,CAAC,usiCAA+siC;AAAA,IAC1tiC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;", "names": []}