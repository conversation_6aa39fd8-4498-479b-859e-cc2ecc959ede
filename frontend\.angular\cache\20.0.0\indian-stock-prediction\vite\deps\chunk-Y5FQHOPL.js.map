{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/dispose-view-repeater-strategy-D_JReLI1.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/unique-selection-dispatcher-Cewa_Eg3.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/selection-model-BCgC8uEN.mjs"], "sourcesContent": ["import { a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n                operation = _ViewRepeaterOperation.INSERTED;\n            }\n            else if (currentIndex == null) {\n                viewContainerRef.remove(adjustedPreviousIndex);\n                operation = _ViewRepeaterOperation.REMOVED;\n            }\n            else {\n                view = viewContainerRef.get(adjustedPreviousIndex);\n                viewContainerRef.move(view, currentIndex);\n                operation = _ViewRepeaterOperation.MOVED;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() { }\n}\n\nexport { _DisposeViewRepeaterStrategy as _ };\n\n", "import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  _listeners = [];\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n  static ɵfac = function UniqueSelectionDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UniqueSelectionDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UniqueSelectionDispatcher,\n    factory: UniqueSelectionDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { UniqueSelectionDispatcher as U };\n", "import { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    _multiple;\n    _emitChanges;\n    compareWith;\n    /** Currently-selected values. */\n    _selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    _deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    _selectedToEmit = [];\n    /** Cache for the array value of the selected items. */\n    _selected;\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    /** Event emitted when the value has changed. */\n    changed = new Subject();\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue, selection) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            selection = selection ?? this._selection;\n            for (let selectedValue of selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\nexport { SelectionModel as S, getMultipleValuesInSingleSelectionError as g };\n\n"], "mappings": ";;;;;;;;;;;;;AAWA,IAAM,+BAAN,MAAmC;AAAA,EAC/B,aAAa,SAAS,kBAAkB,oBAAoB,mBAAmB,iBAAiB;AAC5F,YAAQ,iBAAiB,CAAC,QAAQ,uBAAuB,iBAAiB;AACtE,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,iBAAiB,MAAM;AAC9B,cAAM,gBAAgB,mBAAmB,QAAQ,uBAAuB,YAAY;AACpF,eAAO,iBAAiB,mBAAmB,cAAc,aAAa,cAAc,SAAS,cAAc,KAAK;AAChH,oBAAY,uBAAuB;AAAA,MACvC,WACS,gBAAgB,MAAM;AAC3B,yBAAiB,OAAO,qBAAqB;AAC7C,oBAAY,uBAAuB;AAAA,MACvC,OACK;AACD,eAAO,iBAAiB,IAAI,qBAAqB;AACjD,yBAAiB,KAAK,MAAM,YAAY;AACxC,oBAAY,uBAAuB;AAAA,MACvC;AACA,UAAI,iBAAiB;AACjB,wBAAgB;AAAA,UACZ,SAAS,MAAM;AAAA,UACf;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,SAAS;AAAA,EAAE;AACf;;;AC5BA,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,OAAO,IAAI,MAAM;AACf,aAAS,YAAY,KAAK,YAAY;AACpC,eAAS,IAAI,IAAI;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU;AACf,SAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO,MAAM;AACX,WAAK,aAAa,KAAK,WAAW,OAAO,gBAAc;AACrD,eAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,IACnC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AClDH,IAAM,iBAAN,MAAqB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa,oBAAI,IAAI;AAAA;AAAA,EAErB,oBAAoB,CAAC;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA,IAAI,WAAW;AACX,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,YAAY,MAAM,KAAK,KAAK,WAAW,OAAO,CAAC;AAAA,IACxD;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU,IAAI,QAAQ;AAAA,EACtB,YAAY,YAAY,OAAO,yBAAyB,eAAe,MAAM,aAAa;AACtF,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,QAAI,2BAA2B,wBAAwB,QAAQ;AAC3D,UAAI,WAAW;AACX,gCAAwB,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AAAA,MACtE,OACK;AACD,aAAK,cAAc,wBAAwB,CAAC,CAAC;AAAA,MACjD;AAEA,WAAK,gBAAgB,SAAS;AAAA,IAClC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AACd,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,QAAQ;AAChB,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AACnD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,QAAQ;AACpB,SAAK,uBAAuB,MAAM;AAClC,UAAM,YAAY,KAAK;AACvB,UAAM,iBAAiB,IAAI,IAAI,OAAO,IAAI,WAAS,KAAK,kBAAkB,KAAK,CAAC,CAAC;AACjF,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,cACK,OAAO,WAAS,CAAC,eAAe,IAAI,KAAK,kBAAkB,OAAO,cAAc,CAAC,CAAC,EAClF,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AACjD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACV,WAAO,KAAK,WAAW,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,OAAO,KAAK;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,aAAa,MAAM;AACrB,SAAK,WAAW;AAChB,UAAM,UAAU,KAAK,kBAAkB;AACvC,QAAI,YAAY;AACZ,WAAK,iBAAiB;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,WAAO,KAAK,WAAW,IAAI,KAAK,kBAAkB,KAAK,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,WAAO,KAAK,WAAW,SAAS;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,CAAC,KAAK,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,WAAW;AACZ,QAAI,KAAK,aAAa,KAAK,UAAU;AACjC,WAAK,UAAU,KAAK,SAAS;AAAA,IACjC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAClB,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,mBAAmB;AAEf,SAAK,YAAY;AACjB,QAAI,KAAK,gBAAgB,UAAU,KAAK,kBAAkB,QAAQ;AAC9D,WAAK,QAAQ,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,MAClB,CAAC;AACD,WAAK,oBAAoB,CAAC;AAC1B,WAAK,kBAAkB,CAAC;AAAA,IAC5B;AAAA,EACJ;AAAA;AAAA,EAEA,cAAc,OAAO;AACjB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AACzB,UAAI,CAAC,KAAK,WAAW;AACjB,aAAK,WAAW;AAAA,MACpB;AACA,UAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AACzB,aAAK,WAAW,IAAI,KAAK;AAAA,MAC7B;AACA,UAAI,KAAK,cAAc;AACnB,aAAK,gBAAgB,KAAK,KAAK;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACnB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,KAAK,WAAW,KAAK,GAAG;AACxB,WAAK,WAAW,OAAO,KAAK;AAC5B,UAAI,KAAK,cAAc;AACnB,aAAK,kBAAkB,KAAK,KAAK;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,aAAa;AACT,QAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAK,WAAW,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAChE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,QAAQ;AAC3B,QAAI,OAAO,SAAS,KAAK,CAAC,KAAK,cAAc,OAAO,cAAc,eAAe,YAAY;AACzF,YAAM,wCAAwC;AAAA,IAClD;AAAA,EACJ;AAAA;AAAA,EAEA,oBAAoB;AAChB,WAAO,CAAC,EAAE,KAAK,kBAAkB,UAAU,KAAK,gBAAgB;AAAA,EACpE;AAAA;AAAA,EAEA,kBAAkB,YAAY,WAAW;AACrC,QAAI,CAAC,KAAK,aAAa;AACnB,aAAO;AAAA,IACX,OACK;AACD,kBAAY,aAAa,KAAK;AAC9B,eAAS,iBAAiB,WAAW;AACjC,YAAI,KAAK,YAAY,YAAY,aAAa,GAAG;AAC7C,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAMA,SAAS,0CAA0C;AAC/C,SAAO,MAAM,yEAAyE;AAC1F;", "names": []}