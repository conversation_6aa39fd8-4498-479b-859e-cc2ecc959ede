var wp=Object.defineProperty,Cp=Object.defineProperties;var bp=Object.getOwnPropertyDescriptors;var sr=Object.getOwnPropertySymbols;var Nc=Object.prototype.hasOwnProperty,xc=Object.prototype.propertyIsEnumerable;var Mc=(e,t,n)=>t in e?wp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,q=(e,t)=>{for(var n in t||={})Nc.call(t,n)&&Mc(e,n,t[n]);if(sr)for(var n of sr(t))xc.call(t,n)&&Mc(e,n,t[n]);return e},W=(e,t)=>Cp(e,bp(t));var VE=(e,t)=>{var n={};for(var r in e)Nc.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&sr)for(var r of sr(e))t.indexOf(r)<0&&xc.call(e,r)&&(n[r]=e[r]);return n};var di;function fi(){return di}function Se(e){let t=di;return di=e,t}var Tp=Symbol("NotFound"),ar=class extends Error{name="\u0275NotFound";constructor(t){super(t)}};function At(e){return e===Tp||e?.name==="\u0275NotFound"}function fr(e,t){return Object.is(e,t)}var j=null,cr=!1,pi=1,_p=null,V=Symbol("SIGNAL");function w(e){let t=j;return j=e,t}function pr(){return j}var Be={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function rt(e){if(cr)throw new Error("");if(j===null)return;j.consumerOnSignalRead(e);let t=j.nextProducerIndex++;if(mr(j),t<j.producerNode.length&&j.producerNode[t]!==e&&ln(j)){let n=j.producerNode[t];gr(n,j.producerIndexOfThis[t])}j.producerNode[t]!==e&&(j.producerNode[t]=e,j.producerIndexOfThis[t]=ln(j)?Rc(e,j,t):0),j.producerLastReadVersion[t]=e.version}function Sc(){pi++}function hr(e){if(!(ln(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===pi)){if(!e.producerMustRecompute(e)&&!ot(e)){dr(e);return}e.producerRecomputeValue(e),dr(e)}}function hi(e){if(e.liveConsumerNode===void 0)return;let t=cr;cr=!0;try{for(let n of e.liveConsumerNode)n.dirty||Mp(n)}finally{cr=t}}function gi(){return j?.consumerAllowSignalWrites!==!1}function Mp(e){e.dirty=!0,hi(e),e.consumerMarkedDirty?.(e)}function dr(e){e.dirty=!1,e.lastCleanEpoch=pi}function Re(e){return e&&(e.nextProducerIndex=0),w(e)}function $e(e,t){if(w(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(ln(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)gr(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function ot(e){mr(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(hr(n),r!==n.version))return!0}return!1}function kt(e){if(mr(e),ln(e))for(let t=0;t<e.producerNode.length;t++)gr(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Rc(e,t,n){if(Oc(e),e.liveConsumerNode.length===0&&Ac(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Rc(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function gr(e,t){if(Oc(e),e.liveConsumerNode.length===1&&Ac(e))for(let r=0;r<e.producerNode.length;r++)gr(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];mr(o),o.producerIndexOfThis[r]=t}}function ln(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function mr(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Oc(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Ac(e){return e.producerNode!==void 0}function yr(e){_p?.(e)}function un(e,t){let n=Object.create(Np);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(hr(n),rt(n),n.value===cn)throw n.error;return n.value};return r[V]=n,yr(n),r}var lr=Symbol("UNSET"),ur=Symbol("COMPUTING"),cn=Symbol("ERRORED"),Np=W(q({},Be),{value:lr,dirty:!0,error:null,equal:fr,kind:"computed",producerMustRecompute(e){return e.value===lr||e.value===ur},producerRecomputeValue(e){if(e.value===ur)throw new Error("");let t=e.value;e.value=ur;let n=Re(e),r,o=!1;try{r=e.computation(),w(null),o=t!==lr&&t!==cn&&r!==cn&&e.equal(t,r)}catch(i){r=cn,e.error=i}finally{$e(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function xp(){throw new Error}var kc=xp;function Pc(e){kc(e)}function mi(e){kc=e}var Sp=null;function yi(e,t){let n=Object.create(dn);n.value=e,t!==void 0&&(n.equal=t);let r=()=>Lc(n);return r[V]=n,yr(n),r}function Lc(e){return rt(e),e.value}function it(e,t){gi()||Pc(e),e.equal(e.value,t)||(e.value=t,Rp(e))}function vr(e,t){gi()||Pc(e),it(e,t(e.value))}var dn=W(q({},Be),{equal:fr,value:void 0,kind:"signal"});function Rp(e){e.version++,Sc(),hi(e),Sp?.(e)}function C(e){return typeof e=="function"}function Pt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Ir=Pt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function st(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var F=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(C(r))try{r()}catch(i){t=i instanceof Ir?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Fc(i)}catch(s){t=t??[],s instanceof Ir?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Ir(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Fc(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&st(n,t)}remove(t){let{_finalizers:n}=this;n&&st(n,t),t instanceof e&&t._removeParent(this)}};F.EMPTY=(()=>{let e=new F;return e.closed=!0,e})();var vi=F.EMPTY;function Er(e){return e instanceof F||e&&"closed"in e&&C(e.remove)&&C(e.add)&&C(e.unsubscribe)}function Fc(e){C(e)?e():e.unsubscribe()}var ue={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Lt={setTimeout(e,t,...n){let{delegate:r}=Lt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Lt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Dr(e){Lt.setTimeout(()=>{let{onUnhandledError:t}=ue;if(t)t(e);else throw e})}function fn(){}var jc=Ii("C",void 0,void 0);function Vc(e){return Ii("E",void 0,e)}function Hc(e){return Ii("N",e,void 0)}function Ii(e,t,n){return{kind:e,value:t,error:n}}var at=null;function Ft(e){if(ue.useDeprecatedSynchronousErrorHandling){let t=!at;if(t&&(at={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=at;if(at=null,n)throw r}}else e()}function Bc(e){ue.useDeprecatedSynchronousErrorHandling&&at&&(at.errorThrown=!0,at.error=e)}var ct=class extends F{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Er(t)&&t.add(this)):this.destination=kp}static create(t,n,r){return new Oe(t,n,r)}next(t){this.isStopped?Di(Hc(t),this):this._next(t)}error(t){this.isStopped?Di(Vc(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Di(jc,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Op=Function.prototype.bind;function Ei(e,t){return Op.call(e,t)}var wi=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){wr(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){wr(r)}else wr(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){wr(n)}}},Oe=class extends ct{constructor(t,n,r){super();let o;if(C(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&ue.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Ei(t.next,i),error:t.error&&Ei(t.error,i),complete:t.complete&&Ei(t.complete,i)}):o=t}this.destination=new wi(o)}};function wr(e){ue.useDeprecatedSynchronousErrorHandling?Bc(e):Dr(e)}function Ap(e){throw e}function Di(e,t){let{onStoppedNotification:n}=ue;n&&Lt.setTimeout(()=>n(e,t))}var kp={closed:!0,next:fn,error:Ap,complete:fn};var jt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Q(e){return e}function Pp(...e){return Ci(e)}function Ci(e){return e.length===0?Q:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var M=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Fp(n)?n:new Oe(n,r,o);return Ft(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=$c(r),new r((o,i)=>{let s=new Oe({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[jt](){return this}pipe(...n){return Ci(n)(this)}toPromise(n){return n=$c(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function $c(e){var t;return(t=e??ue.Promise)!==null&&t!==void 0?t:Promise}function Lp(e){return e&&C(e.next)&&C(e.error)&&C(e.complete)}function Fp(e){return e&&e instanceof ct||Lp(e)&&Er(e)}function bi(e){return C(e?.lift)}function E(e){return t=>{if(bi(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function v(e,t,n,r,o){return new Ti(e,t,n,r,o)}var Ti=class extends ct{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function _i(){return E((e,t)=>{let n=null;e._refCount++;let r=v(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Mi=class extends M{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,bi(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new F;let n=this.getSubject();t.add(this.source.subscribe(v(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=F.EMPTY)}return t}refCount(){return _i()(this)}};var Uc=Pt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var re=(()=>{class e extends M{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Cr(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Uc}next(n){Ft(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Ft(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Ft(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?vi:(this.currentObservers=null,i.push(n),new F(()=>{this.currentObservers=null,st(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new M;return n.source=this,n}}return e.create=(t,n)=>new Cr(t,n),e})(),Cr=class extends re{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:vi}};var pn=class extends re{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var hn={now(){return(hn.delegate||Date).now()},delegate:void 0};var br=class extends re{constructor(t=1/0,n=1/0,r=hn){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Tr=class extends F{constructor(t,n){super()}schedule(t,n=0){return this}};var gn={setInterval(e,t,...n){let{delegate:r}=gn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=gn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var _r=class extends Tr{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return gn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&gn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,st(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Vt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Vt.now=hn.now;var Mr=class extends Vt{constructor(t,n=Vt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var mn=new Mr(_r),qc=mn;var lt=new M(e=>e.complete());function Nr(e){return e&&C(e.schedule)}function Ni(e){return e[e.length-1]}function xr(e){return C(Ni(e))?e.pop():void 0}function Ie(e){return Nr(Ni(e))?e.pop():void 0}function Wc(e,t){return typeof Ni(e)=="number"?e.pop():t}function zc(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function Gc(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function ut(e){return this instanceof ut?(this.v=e,this):new ut(e)}function Qc(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(N,b){i.push([f,g,N,b])>1||c(f,g)})},h&&(o[f]=h(o[f])))}function c(f,h){try{l(r[f](h))}catch(g){p(i[0][3],g)}}function l(f){f.value instanceof ut?Promise.resolve(f.value.v).then(u,d):p(i[0][2],f)}function u(f){c("next",f)}function d(f){c("throw",f)}function p(f,h){f(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Zc(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Gc=="function"?Gc(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var Sr=e=>e&&typeof e.length=="number"&&typeof e!="function";function Rr(e){return C(e?.then)}function Or(e){return C(e[jt])}function Ar(e){return Symbol.asyncIterator&&C(e?.[Symbol.asyncIterator])}function kr(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function jp(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Pr=jp();function Lr(e){return C(e?.[Pr])}function Fr(e){return Qc(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield ut(n.read());if(o)return yield ut(void 0);yield yield ut(r)}}finally{n.releaseLock()}})}function jr(e){return C(e?.getReader)}function R(e){if(e instanceof M)return e;if(e!=null){if(Or(e))return Vp(e);if(Sr(e))return Hp(e);if(Rr(e))return Bp(e);if(Ar(e))return Yc(e);if(Lr(e))return $p(e);if(jr(e))return Up(e)}throw kr(e)}function Vp(e){return new M(t=>{let n=e[jt]();if(C(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Hp(e){return new M(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Bp(e){return new M(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Dr)})}function $p(e){return new M(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Yc(e){return new M(t=>{qp(e,t).catch(n=>t.error(n))})}function Up(e){return Yc(Fr(e))}function qp(e,t){var n,r,o,i;return zc(this,void 0,void 0,function*(){try{for(n=Zc(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function X(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Vr(e,t=0){return E((n,r)=>{n.subscribe(v(r,o=>X(r,e,()=>r.next(o),t),()=>X(r,e,()=>r.complete(),t),o=>X(r,e,()=>r.error(o),t)))})}function Hr(e,t=0){return E((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Jc(e,t){return R(e).pipe(Hr(t),Vr(t))}function Kc(e,t){return R(e).pipe(Hr(t),Vr(t))}function Xc(e,t){return new M(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function el(e,t){return new M(n=>{let r;return X(n,t,()=>{r=e[Pr](),X(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>C(r?.return)&&r.return()})}function Br(e,t){if(!e)throw new Error("Iterable cannot be null");return new M(n=>{X(n,t,()=>{let r=e[Symbol.asyncIterator]();X(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function tl(e,t){return Br(Fr(e),t)}function nl(e,t){if(e!=null){if(Or(e))return Jc(e,t);if(Sr(e))return Xc(e,t);if(Rr(e))return Kc(e,t);if(Ar(e))return Br(e,t);if(Lr(e))return el(e,t);if(jr(e))return tl(e,t)}throw kr(e)}function Ee(e,t){return t?nl(e,t):R(e)}function Wp(...e){let t=Ie(e);return Ee(e,t)}function Gp(e,t){let n=C(e)?e:()=>e,r=o=>o.error(n());return new M(t?o=>t.schedule(r,0,o):r)}function zp(e){return!!e&&(e instanceof M||C(e.lift)&&C(e.subscribe))}var dt=Pt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function rl(e){return e instanceof Date&&!isNaN(e)}function ft(e,t){return E((n,r)=>{let o=0;n.subscribe(v(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Qp}=Array;function Zp(e,t){return Qp(t)?e(...t):e(t)}function $r(e){return ft(t=>Zp(e,t))}var{isArray:Yp}=Array,{getPrototypeOf:Jp,prototype:Kp,keys:Xp}=Object;function Ur(e){if(e.length===1){let t=e[0];if(Yp(t))return{args:t,keys:null};if(eh(t)){let n=Xp(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function eh(e){return e&&typeof e=="object"&&Jp(e)===Kp}function qr(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function th(...e){let t=Ie(e),n=xr(e),{args:r,keys:o}=Ur(e);if(r.length===0)return Ee([],t);let i=new M(nh(r,t,o?s=>qr(o,s):Q));return n?i.pipe($r(n)):i}function nh(e,t,n=Q){return r=>{ol(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ol(t,()=>{let l=Ee(e[c],t),u=!1;l.subscribe(v(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ol(e,t,n){e?X(n,e,t):t()}function il(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,p=()=>{d&&!c.length&&!l&&t.complete()},f=g=>l<r?h(g):c.push(g),h=g=>{i&&t.next(g),l++;let N=!1;R(n(g,u++)).subscribe(v(t,b=>{o?.(b),i?f(b):t.next(b)},()=>{N=!0},void 0,()=>{if(N)try{for(l--;c.length&&l<r;){let b=c.shift();s?X(t,s,()=>h(b)):h(b)}p()}catch(b){t.error(b)}}))};return e.subscribe(v(t,f,()=>{d=!0,p()})),()=>{a?.()}}function pt(e,t,n=1/0){return C(t)?pt((r,o)=>ft((i,s)=>t(r,i,o,s))(R(e(r,o))),n):(typeof t=="number"&&(n=t),E((r,o)=>il(r,o,e,n)))}function yn(e=1/0){return pt(Q,e)}function sl(){return yn(1)}function Wr(...e){return sl()(Ee(e,Ie(e)))}function rh(e){return new M(t=>{R(e()).subscribe(t)})}function oh(...e){let t=xr(e),{args:n,keys:r}=Ur(e),o=new M(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;R(n[u]).subscribe(v(i,p=>{d||(d=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?qr(r,a):a),i.complete())}))}});return t?o.pipe($r(t)):o}function xi(e=0,t,n=qc){let r=-1;return t!=null&&(Nr(t)?n=t:r=t),new M(o=>{let i=rl(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function ih(...e){let t=Ie(e),n=Wc(e,1/0),r=e;return r.length?r.length===1?R(r[0]):yn(n)(Ee(r,t)):lt}function ht(e,t){return E((n,r)=>{let o=0;n.subscribe(v(r,i=>e.call(t,i,o++)&&r.next(i)))})}function al(e){return E((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(v(n,l=>{r=!0,o=l,i||R(e(l)).subscribe(i=v(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function sh(e,t=mn){return al(()=>xi(e,t))}function cl(e){return E((t,n)=>{let r=null,o=!1,i;r=t.subscribe(v(n,void 0,void 0,s=>{i=R(e(s,cl(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function ll(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(v(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function ah(e,t){return C(t)?pt(e,t,1):pt(e,1)}function ch(e,t=mn){return E((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function c(){let l=s+e,u=t.now();if(u<l){o=this.schedule(void 0,l-u),r.add(o);return}a()}n.subscribe(v(r,l=>{i=l,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function vn(e){return E((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Si(e){return e<=0?()=>lt:E((t,n)=>{let r=0;t.subscribe(v(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function lh(e,t=Q){return e=e??uh,E((n,r)=>{let o,i=!0;n.subscribe(v(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function uh(e,t){return e===t}function Gr(e=dh){return E((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function dh(){return new dt}function fh(e){return E((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function ph(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ht((o,i)=>e(o,i,r)):Q,Si(1),n?vn(t):Gr(()=>new dt))}function Ri(e){return e<=0?()=>lt:E((t,n)=>{let r=[];t.subscribe(v(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function hh(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ht((o,i)=>e(o,i,r)):Q,Ri(1),n?vn(t):Gr(()=>new dt))}function gh(){return E((e,t)=>{let n,r=!1;e.subscribe(v(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function mh(e,t){return E(ll(e,t,arguments.length>=2,!0))}function Ai(e={}){let{connector:t=()=>new re,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,l=0,u=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=c=void 0,u=d=!1},h=()=>{let g=s;f(),g?.unsubscribe()};return E((g,N)=>{l++,!d&&!u&&p();let b=c=c??t();N.add(()=>{l--,l===0&&!d&&!u&&(a=Oi(h,o))}),b.subscribe(N),!s&&l>0&&(s=new Oe({next:ve=>b.next(ve),error:ve=>{d=!0,p(),a=Oi(f,n,ve),b.error(ve)},complete:()=>{u=!0,p(),a=Oi(f,r),b.complete()}}),R(g).subscribe(s))})(i)}}function Oi(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Oe({next:()=>{r.unsubscribe(),e()}});return R(t(...n)).subscribe(r)}function yh(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,Ai({connector:()=>new br(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function vh(e){return ht((t,n)=>e<=n)}function Ih(...e){let t=Ie(e);return E((n,r)=>{(t?Wr(e,n,t):Wr(e,n)).subscribe(r)})}function Eh(e,t){return E((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(v(r,c=>{o?.unsubscribe();let l=0,u=i++;R(e(c,u)).subscribe(o=v(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Dh(e){return E((t,n)=>{R(e).subscribe(v(n,()=>n.complete(),fn)),!n.closed&&t.subscribe(n)})}function wh(e,t=!1){return E((n,r)=>{let o=0;n.subscribe(v(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function Ch(e,t,n){let r=C(e)||t||n?{next:e,error:t,complete:n}:e;return r?E((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(v(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Q}function ul(e){let t=w(null);try{return e()}finally{w(t)}}var Jr="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",_=class extends Error{code;constructor(t,n){super(Kr(t,n)),this.code=t}};function bh(e){return`NG0${Math.abs(e)}`}function Kr(e,t){return`${bh(e)}${t?": "+t:""}`}var we=globalThis;function k(e){for(let t in e)if(e[t]===k)return t;throw Error("")}function hl(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function ee(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(ee).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Xr(e,t){return e?t?`${e} ${t}`:e:t||""}var Th=k({__forward_ref__:k});function eo(e){return e.__forward_ref__=eo,e.toString=function(){return ee(this())},e}function G(e){return Wi(e)?e():e}function Wi(e){return typeof e=="function"&&e.hasOwnProperty(Th)&&e.__forward_ref__===eo}function gl(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function B(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ml(e){return{providers:e.providers||[],imports:e.imports||[]}}function Cn(e){return Mh(e,to)}function _h(e){return Cn(e)!==null}function Mh(e,t){return e.hasOwnProperty(t)&&e[t]||null}function Nh(e){let t=e?.[to]??null;return t||null}function Pi(e){return e&&e.hasOwnProperty(Qr)?e[Qr]:null}var to=k({\u0275prov:k}),Qr=k({\u0275inj:k}),x=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=B({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Gi(e){return e&&!!e.\u0275providers}var zi=k({\u0275cmp:k}),Qi=k({\u0275dir:k}),Zi=k({\u0275pipe:k}),Yi=k({\u0275mod:k}),En=k({\u0275fac:k}),It=k({__NG_ELEMENT_ID__:k}),dl=k({__NG_ENV_ID__:k});function bn(e){return typeof e=="string"?e:e==null?"":String(e)}function yl(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():bn(e)}function Ji(e,t){throw new _(-200,e)}function no(e,t){throw new _(-201,!1)}var Li;function vl(){return Li}function Z(e){let t=Li;return Li=e,t}function Ki(e,t,n){let r=Cn(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;no(e,"Injector")}var xh={},gt=xh,Fi="__NG_DI_FLAG__",ji=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=mt(n)||0;try{return this.injector.get(t,r&8?null:gt,r)}catch(o){if(At(o))return o;throw o}}},Zr="ngTempTokenPath",Sh="ngTokenPath",Rh=/\n/gm,Oh="\u0275",fl="__source";function Ah(e,t=0){let n=fi();if(n===void 0)throw new _(-203,!1);if(n===null)return Ki(e,void 0,t);{let r=kh(t),o=n.retrieve(e,r);if(At(o)){if(r.optional)return null;throw o}return o}}function De(e,t=0){return(vl()||Ah)(G(e),t)}function m(e,t){return De(e,mt(t))}function mt(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function kh(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Vi(e){let t=[];for(let n=0;n<e.length;n++){let r=G(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=Ph(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(De(o,i))}else t.push(De(r))}return t}function Xi(e,t){return e[Fi]=t,e.prototype[Fi]=t,e}function Ph(e){return e[Fi]}function Lh(e,t,n,r){let o=e[Zr];throw t[fl]&&o.unshift(t[fl]),e.message=Fh(`
`+e.message,o,n,r),e[Sh]=o,e[Zr]=null,e}function Fh(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Oh?e.slice(2):e;let o=ee(t);if(Array.isArray(t))o=t.map(ee).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ee(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Rh,`
  `)}`}function Ue(e,t){let n=e.hasOwnProperty(En);return n?e[En]:null}function Il(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function El(e){return e.flat(Number.POSITIVE_INFINITY)}function ro(e,t){e.forEach(n=>Array.isArray(n)?ro(n,t):t(n))}function es(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Tn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Dl(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function wl(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function oo(e,t,n){let r=Bt(e,t);return r>=0?e[r|1]=n:(r=~r,wl(e,r,t,n)),r}function io(e,t){let n=Bt(e,t);if(n>=0)return e[n|1]}function Bt(e,t){return jh(e,t,1)}function jh(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var We={},z=[],Ge=new x(""),ts=new x("",-1),ns=new x(""),Dn=class{get(t,n=gt){if(n===gt)throw new ar(`NullInjectorError: No provider for ${ee(t)}!`);return n}};function rs(e){return e[Yi]||null}function Ce(e){return e[zi]||null}function os(e){return e[Qi]||null}function Cl(e){return e[Zi]||null}function $t(e){return{\u0275providers:e}}function bl(e){return $t([{provide:Ge,multi:!0,useValue:e}])}function Tl(...e){return{\u0275providers:is(!0,e),\u0275fromNgModule:!0}}function is(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ro(t,s=>{let a=s;Yr(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&_l(o,i),n}function _l(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ss(o,i=>{t(i,r)})}}function Yr(e,t,n,r){if(e=G(e),!e)return!1;let o=null,i=Pi(e),s=!i&&Ce(e);if(!i&&!s){let c=e.ngModule;if(i=Pi(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Yr(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{ro(i.imports,u=>{Yr(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&_l(l,t)}if(!a){let l=Ue(o)||(()=>new o);t({provide:o,useFactory:l,deps:z},o),t({provide:ns,useValue:o,multi:!0},o),t({provide:Ge,useValue:()=>De(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;ss(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function ss(e,t){for(let n of e)Gi(n)&&(n=n.\u0275providers),Array.isArray(n)?ss(n,t):t(n)}var Vh=k({provide:String,useValue:k});function Ml(e){return e!==null&&typeof e=="object"&&Vh in e}function Hh(e){return!!(e&&e.useExisting)}function Bh(e){return!!(e&&e.useFactory)}function yt(e){return typeof e=="function"}function Nl(e){return!!e.useClass}var as=new x(""),zr={},pl={},ki;function Ut(){return ki===void 0&&(ki=new Dn),ki}var oe=class{},vt=class extends oe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Bi(t,s=>this.processProvider(s)),this.records.set(ts,Ht(void 0,this)),o.has("environment")&&this.records.set(oe,Ht(void 0,this));let i=this.records.get(as);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(ns,z,{self:!0}))}retrieve(t,n){let r=mt(n)||0;try{return this.get(t,gt,r)}catch(o){if(At(o))return o;throw o}}destroy(){In(this),this._destroyed=!0;let t=w(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),w(t)}}onDestroy(t){return In(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){In(this);let n=Se(this),r=Z(void 0),o;try{return t()}finally{Se(n),Z(r)}}get(t,n=gt,r){if(In(this),t.hasOwnProperty(dl))return t[dl](this);let o=mt(r),i,s=Se(this),a=Z(void 0);try{if(!(o&4)){let l=this.records.get(t);if(l===void 0){let u=Gh(t)&&Cn(t);u&&this.injectableDefInScope(u)?l=Ht(Hi(t),zr):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let c=o&2?Ut():this.parent;return n=o&8&&n===gt?null:n,c.get(t,n)}catch(c){if(At(c)){if((c[Zr]=c[Zr]||[]).unshift(ee(t)),s)throw c;return Lh(c,t,"R3InjectorError",this.source)}else throw c}finally{Z(a),Se(s)}}resolveInjectorInitializers(){let t=w(null),n=Se(this),r=Z(void 0),o;try{let i=this.get(Ge,z,{self:!0});for(let s of i)s()}finally{Se(n),Z(r),w(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(ee(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=G(t);let n=yt(t)?t:G(t&&t.provide),r=Uh(t);if(!yt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Ht(void 0,zr,!0),o.factory=()=>Vi(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=w(null);try{return n.value===pl?Ji(ee(t)):n.value===zr&&(n.value=pl,n.value=n.factory()),typeof n.value=="object"&&n.value&&Wh(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{w(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=G(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Hi(e){let t=Cn(e),n=t!==null?t.factory:Ue(e);if(n!==null)return n;if(e instanceof x)throw new _(204,!1);if(e instanceof Function)return $h(e);throw new _(204,!1)}function $h(e){if(e.length>0)throw new _(204,!1);let n=Nh(e);return n!==null?()=>n.factory(e):()=>new e}function Uh(e){if(Ml(e))return Ht(void 0,e.useValue);{let t=cs(e);return Ht(t,zr)}}function cs(e,t,n){let r;if(yt(e)){let o=G(e);return Ue(o)||Hi(o)}else if(Ml(e))r=()=>G(e.useValue);else if(Bh(e))r=()=>e.useFactory(...Vi(e.deps||[]));else if(Hh(e))r=()=>De(G(e.useExisting));else{let o=G(e&&(e.useClass||e.provide));if(qh(e))r=()=>new o(...Vi(e.deps));else return Ue(o)||Hi(o)}return r}function In(e){if(e.destroyed)throw new _(205,!1)}function Ht(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function qh(e){return!!e.deps}function Wh(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Gh(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Bi(e,t){for(let n of e)Array.isArray(n)?Bi(n,t):n&&Gi(n)?Bi(n.\u0275providers,t):t(n)}function so(e,t){let n;e instanceof vt?(In(e),n=e):n=new ji(e);let r,o=Se(n),i=Z(void 0);try{return t()}finally{Se(o),Z(i)}}function ls(){return vl()!==void 0||fi()!=null}function us(e){if(!ls())throw new _(-203,!1)}var fe=0,D=1,y=2,H=3,ie=4,Y=5,Et=6,qt=7,P=8,Dt=9,be=10,O=11,Wt=12,ds=13,wt=14,J=15,ze=16,Ct=17,Te=18,_n=19,fs=20,Ae=21,ao=22,Pe=23,ne=24,bt=25,L=26,xl=1,ps=6,Qe=7,Mn=8,Tt=9,$=10;function _e(e){return Array.isArray(e)&&typeof e[xl]=="object"}function pe(e){return Array.isArray(e)&&e[xl]===!0}function co(e){return(e.flags&4)!==0}function Ze(e){return e.componentOffset>-1}function Nn(e){return(e.flags&1)===1}function Me(e){return!!e.template}function Gt(e){return(e[y]&512)!==0}function zt(e){return(e[y]&256)===256}var hs="svg",Sl="math";function se(e){for(;Array.isArray(e);)e=e[fe];return e}function gs(e,t){return se(t[e])}function he(e,t){return se(t[e.index])}function xn(e,t){return e.data[t]}function lo(e,t){return e[t]}function ms(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function ae(e,t){let n=t[e];return _e(n)?n:n[fe]}function Rl(e){return(e[y]&4)===4}function uo(e){return(e[y]&128)===128}function Ol(e){return pe(e[H])}function ge(e,t){return t==null?null:e[t]}function ys(e){e[Ct]=0}function vs(e){e[y]&1024||(e[y]|=1024,uo(e)&&Ye(e))}function Al(e,t){for(;e>0;)t=t[wt],e--;return t}function Sn(e){return!!(e[y]&9216||e[ne]?.dirty)}function fo(e){e[be].changeDetectionScheduler?.notify(8),e[y]&64&&(e[y]|=1024),Sn(e)&&Ye(e)}function Ye(e){e[be].changeDetectionScheduler?.notify(0);let t=qe(e);for(;t!==null&&!(t[y]&8192||(t[y]|=8192,!uo(t)));)t=qe(t)}function Is(e,t){if(zt(e))throw new _(911,!1);e[Ae]===null&&(e[Ae]=[]),e[Ae].push(t)}function kl(e,t){if(e[Ae]===null)return;let n=e[Ae].indexOf(t);n!==-1&&e[Ae].splice(n,1)}function qe(e){let t=e[H];return pe(t)?t[H]:t}function Es(e){return e[qt]??=[]}function Ds(e){return e.cleanup??=[]}function Pl(e,t,n,r){let o=Es(t);o.push(n),e.firstCreatePass&&Ds(e).push(r,o.length-1)}var T={lFrame:Jl(null),bindingsEnabled:!0,skipHydrationRootTNode:null},Rn=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(Rn||{}),zh=0,$i=!1;function Ll(){return T.lFrame.elementDepthCount}function Fl(){T.lFrame.elementDepthCount++}function jl(){T.lFrame.elementDepthCount--}function po(){return T.bindingsEnabled}function ws(){return T.skipHydrationRootTNode!==null}function Vl(e){return T.skipHydrationRootTNode===e}function Hl(){T.skipHydrationRootTNode=null}function I(){return T.lFrame.lView}function A(){return T.lFrame.tView}function Bl(e){return T.lFrame.contextLView=e,e[P]}function $l(e){return T.lFrame.contextLView=null,e}function U(){let e=Cs();for(;e!==null&&e.type===64;)e=e.parent;return e}function Cs(){return T.lFrame.currentTNode}function Ul(){let e=T.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Je(e,t){let n=T.lFrame;n.currentTNode=e,n.isParent=t}function ho(){return T.lFrame.isParent}function go(){T.lFrame.isParent=!1}function ql(){return T.lFrame.contextLView}function bs(e){gl("Must never be called in production mode"),zh=e}function Ts(){return $i}function Qt(e){let t=$i;return $i=e,t}function _s(){let e=T.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Wl(e){return T.lFrame.bindingIndex=e}function Ke(){return T.lFrame.bindingIndex++}function Ms(e){let t=T.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Gl(){return T.lFrame.inI18n}function zl(e,t){let n=T.lFrame;n.bindingIndex=n.bindingRootIndex=e,mo(t)}function Ql(){return T.lFrame.currentDirectiveIndex}function mo(e){T.lFrame.currentDirectiveIndex=e}function Zl(e){let t=T.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function yo(){return T.lFrame.currentQueryIndex}function On(e){T.lFrame.currentQueryIndex=e}function Qh(e){let t=e[D];return t.type===2?t.declTNode:t.type===1?e[Y]:null}function Ns(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=Qh(i),o===null||(i=i[wt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=T.lFrame=Yl();return r.currentTNode=t,r.lView=e,!0}function vo(e){let t=Yl(),n=e[D];T.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Yl(){let e=T.lFrame,t=e===null?null:e.child;return t===null?Jl(e):t}function Jl(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Kl(){let e=T.lFrame;return T.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var xs=Kl;function Io(){let e=Kl();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Xl(e){return(T.lFrame.contextLView=Al(e,T.lFrame.contextLView))[P]}function Le(){return T.lFrame.selectedIndex}function Xe(e){T.lFrame.selectedIndex=e}function An(){let e=T.lFrame;return xn(e.tView,e.selectedIndex)}function eu(){T.lFrame.currentNamespace=hs}function tu(){Zh()}function Zh(){T.lFrame.currentNamespace=null}function nu(){return T.lFrame.currentNamespace}var ru=!0;function kn(){return ru}function Pn(e){ru=e}function Ui(e,t=null,n=null,r){let o=Ss(e,t,n,r);return o.resolveInjectorInitializers(),o}function Ss(e,t=null,n=null,r,o=new Set){let i=[n||z,Tl(e)];return r=r||(typeof e=="object"?void 0:ee(e)),new vt(i,t||Ut(),r||null,o)}var de=class e{static THROW_IF_NOT_FOUND=gt;static NULL=new Dn;static create(t,n){if(Array.isArray(t))return Ui({name:""},n,t,"");{let r=t.name??"";return Ui({name:r},t.parent,t.providers,r)}}static \u0275prov=B({token:e,providedIn:"any",factory:()=>De(ts)});static __NG_ELEMENT_ID__=-1},ou=new x(""),Fe=(()=>{class e{static __NG_ELEMENT_ID__=Yh;static __NG_ENV_ID__=n=>n}return e})(),wn=class extends Fe{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Is(n,t),()=>kl(n,t)}};function Yh(){return new wn(I())}var ke=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Ne=new x("",{providedIn:"root",factory:()=>{let e=m(oe),t;return n=>{t??=e.get(ke),t.handleError(n)}}}),iu={provide:Ge,useValue:()=>void m(ke),multi:!0},Jh=new x("",{providedIn:"root",factory:()=>{let e=m(ou).defaultView;if(!e)return;let t=m(Ne),n=i=>{t(i.reason),i.preventDefault()},r=i=>{t(i.error),i.preventDefault()},o=()=>{e.addEventListener("unhandledrejection",n),e.addEventListener("error",r)};typeof Zone<"u"?Zone.root.run(o):o(),m(Fe).onDestroy(()=>{e.removeEventListener("error",r),e.removeEventListener("unhandledrejection",n)})}});function Kh(){return $t([bl(()=>void m(Jh))])}function Rs(e){return typeof e=="function"&&e[V]!==void 0}function Eo(e,t){let n=yi(e,t?.equal),r=n[V];return n.set=o=>it(r,o),n.update=o=>vr(r,o),n.asReadonly=Os.bind(n),n}function Os(){let e=this[V];if(e.readonlyFn===void 0){let t=()=>this();t[V]=e,e.readonlyFn=t}return e.readonlyFn}function As(e){return Rs(e)&&typeof e.set=="function"}var te=class{},Ln=new x("",{providedIn:"root",factory:()=>!1});var ks=new x(""),Do=new x("");var _t=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Xh}return e})();function Xh(){return new _t(I(),U())}var Mt=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new pn(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new M(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})(),Ps=(()=>{class e{internalPendingTasks=m(Mt);scheduler=m(te);errorHandler=m(Ne);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})();function Nt(...e){}var Fn=(()=>{class e{static \u0275prov=B({token:e,providedIn:"root",factory:()=>new qi})}return e})(),qi=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function rn(e){return{toString:e}.toString()}var wo="__parameters__";function sg(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function ju(e,t,n){return rn(()=>{let r=sg(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let d=c.hasOwnProperty(wo)?c[wo]:Object.defineProperty(c,wo,{value:[]})[wo];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Vu=Xi(ju("Optional"),8);var Hu=Xi(ju("SkipSelf"),4);function ag(e){return typeof e=="function"}var Ro=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Bu(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var cg=(()=>{let e=()=>$u;return e.ngInherit=!0,e})();function $u(e){return e.type.prototype.ngOnChanges&&(e.setInput=ug),lg}function lg(){let e=qu(this),t=e?.current;if(t){let n=e.previous;if(n===We)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function ug(e,t,n,r,o){let i=this.declaredInputs[r],s=qu(e)||dg(e,{previous:We,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Ro(l&&l.currentValue,n,c===We),Bu(e,t,o,n)}var Uu="__ngSimpleChanges__";function qu(e){return e[Uu]||null}function dg(e,t){return e[Uu]=t}var su=[];var S=function(e,t=null,n){for(let r=0;r<su.length;r++){let o=su[r];o(e,t,n)}};function fg(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=$u(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Aa(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function Mo(e,t,n){Wu(e,t,3,n)}function No(e,t,n,r){(e[y]&3)===n&&Wu(e,t,n,r)}function Ls(e,t){let n=e[y];(n&3)===t&&(n&=16383,n+=1,e[y]=n)}function Wu(e,t,n,r){let o=r!==void 0?e[Ct]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Ct]+=65536),(a<i||i==-1)&&(pg(e,n,t,c),e[Ct]=(e[Ct]&**********)+c+2),c++}function au(e,t){S(4,e,t);let n=w(null);try{t.call(e)}finally{w(n),S(5,e,t)}}function pg(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[y]>>14<e[Ct]>>16&&(e[y]&3)===t&&(e[y]+=16384,au(a,i)):au(a,i)}var Yt=-1,Rt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function hg(e){return(e.flags&8)!==0}function gg(e){return(e.flags&16)!==0}function mg(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];yg(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Gu(e){return e===3||e===4||e===6}function yg(e){return e.charCodeAt(0)===64}function Jt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?cu(e,n,o,null,t[++r]):cu(e,n,o,null,null))}}return e}function cu(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function zu(e){return e!==Yt}function Oo(e){return e&32767}function vg(e){return e>>16}function Ao(e,t){let n=vg(e),r=t;for(;n>0;)r=r[wt],n--;return r}var Qs=!0;function ko(e){let t=Qs;return Qs=e,t}var Ig=256,Qu=Ig-1,Zu=5,Eg=0,xe={};function Dg(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(It)&&(r=n[It]),r==null&&(r=n[It]=Eg++);let o=r&Qu,i=1<<o;t.data[e+(o>>Zu)]|=i}function Po(e,t){let n=Yu(e,t);if(n!==-1)return n;let r=t[D];r.firstCreatePass&&(e.injectorIndex=t.length,Fs(r.data,e),Fs(t,null),Fs(r.blueprint,null));let o=ka(e,t),i=e.injectorIndex;if(zu(o)){let s=Oo(o),a=Ao(o,t),c=a[D].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Fs(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Yu(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function ka(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=td(o),r===null)return Yt;if(n++,o=o[wt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Yt}function Zs(e,t,n){Dg(e,t,n)}function wg(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Gu(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Ju(e,t,n){if(n&8||e!==void 0)return e;no(t,"NodeInjector")}function Ku(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[Dt],i=Z(void 0);try{return o?o.get(t,r,n&8):Ki(t,r,n&8)}finally{Z(i)}}return Ju(r,t,n)}function Xu(e,t,n,r=0,o){if(e!==null){if(t[y]&2048&&!(r&2)){let s=Mg(e,t,n,r,xe);if(s!==xe)return s}let i=ed(e,t,n,r,xe);if(i!==xe)return i}return Ku(t,n,r,o)}function ed(e,t,n,r,o){let i=bg(n);if(typeof i=="function"){if(!Ns(t,e,r))return r&1?Ju(o,n,r):Ku(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))no(n);else return s}finally{xs()}}else if(typeof i=="number"){let s=null,a=Yu(e,t),c=Yt,l=r&1?t[J][Y]:null;for((a===-1||r&4)&&(c=a===-1?ka(e,t):t[a+8],c===Yt||!uu(r,!1)?a=-1:(s=t[D],a=Oo(c),t=Ao(c,t)));a!==-1;){let u=t[D];if(lu(i,a,u.data)){let d=Cg(a,t,n,s,r,l);if(d!==xe)return d}c=t[a+8],c!==Yt&&uu(r,t[D].data[a+8]===l)&&lu(i,a,t)?(s=u,a=Oo(c),t=Ao(c,t)):a=-1}}return o}function Cg(e,t,n,r,o,i){let s=t[D],a=s.data[e+8],c=r==null?Ze(a)&&Qs:r!=s&&(a.type&3)!==0,l=o&1&&i===a,u=xo(a,s,n,c,l);return u!==null?Hn(t,s,u,a):xe}function xo(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,p=o?a+u:l;for(let f=d;f<p;f++){let h=s[f];if(f<c&&n===h||f>=c&&h.type===n)return f}if(o){let f=s[c];if(f&&Me(f)&&f.type===n)return c}return null}function Hn(e,t,n,r){let o=e[n],i=t.data;if(o instanceof Rt){let s=o;s.resolving&&Ji(yl(i[n]));let a=ko(s.canSeeViewProviders);s.resolving=!0;let c=i[n].type||i[n],l,u=s.injectImpl?Z(s.injectImpl):null,d=Ns(e,r,0);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&fg(n,i[n],t)}finally{u!==null&&Z(u),ko(a),s.resolving=!1,xs()}}return o}function bg(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(It)?e[It]:void 0;return typeof t=="number"?t>=0?t&Qu:Tg:t}function lu(e,t,n){let r=1<<e;return!!(n[t+(e>>Zu)]&r)}function uu(e,t){return!(e&2)&&!(e&1&&t)}var St=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Xu(this._tNode,this._lView,t,mt(r),n)}};function Tg(){return new St(U(),I())}function _g(e){return rn(()=>{let t=e.prototype.constructor,n=t[En]||Ys(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[En]||Ys(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ys(e){return Wi(e)?()=>{let t=Ys(G(e));return t&&t()}:Ue(e)}function Mg(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[y]&2048&&!Gt(s);){let a=ed(i,s,n,r|2,xe);if(a!==xe)return a;let c=i.parent;if(!c){let l=s[fs];if(l){let u=l.get(n,xe,r);if(u!==xe)return u}c=td(s),s=s[wt]}i=c}return o}function td(e){let t=e[D],n=t.type;return n===2?t.declTNode:n===1?e[Y]:null}function nd(e){return wg(U(),e)}function Ng(){return on(U(),I())}function on(e,t){return new Zn(he(e,t))}var Zn=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Ng}return e})();function rd(e){return e instanceof Zn?e.nativeElement:e}function xg(){return this._results[Symbol.iterator]()}var Lo=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new re}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=El(t);(this._changesDetected=!Il(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=xg};function od(e){return(e.flags&128)===128}var Pa=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Pa||{}),id=new Map,Sg=0;function Rg(){return Sg++}function Og(e){id.set(e[_n],e)}function Js(e){id.delete(e[_n])}var du="__ngContext__";function sn(e,t){_e(t)?(e[du]=t[_n],Og(t)):e[du]=t}function sd(e){return cd(e[Wt])}function ad(e){return cd(e[ie])}function cd(e){for(;e!==null&&!pe(e);)e=e[ie];return e}var Ks;function Ag(e){Ks=e}function ld(){if(Ks!==void 0)return Ks;if(typeof document<"u")return document;throw new _(210,!1)}var kg=new x("",{providedIn:"root",factory:()=>Pg}),Pg="ng",ud=new x(""),Lg=new x("",{providedIn:"platform",factory:()=>"unknown"});var Fg=new x(""),jg=new x("",{providedIn:"root",factory:()=>ld().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Vg="h",Hg="b";var dd="r";var fd="di";var pd=!1,hd=new x("",{providedIn:"root",factory:()=>pd});var Bg=(e,t,n,r)=>{};function $g(e,t,n,r){Bg(e,t,n,r)}var Ug=()=>null;function gd(e,t,n=!1){return Ug(e,t,n)}function md(e,t){let n=e.contentQueries;if(n!==null){let r=w(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];On(i),a.contentQueries(2,t[s],s)}}}finally{w(r)}}}function Xs(e,t,n){On(0);let r=w(null);try{t(e,n)}finally{w(r)}}function La(e,t,n){if(co(t)){let r=w(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{w(r)}}}var Kt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Kt||{}),Co;function qg(){if(Co===void 0&&(Co=null,we.trustedTypes))try{Co=we.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Co}function Yo(e){return qg()?.createHTML(e)||e}var bo;function Wg(){if(bo===void 0&&(bo=null,we.trustedTypes))try{bo=we.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return bo}function fu(e){return Wg()?.createScriptURL(e)||e}var Ve=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Jr})`}},ea=class extends Ve{getTypeName(){return"HTML"}},ta=class extends Ve{getTypeName(){return"Style"}},na=class extends Ve{getTypeName(){return"Script"}},ra=class extends Ve{getTypeName(){return"URL"}},oa=class extends Ve{getTypeName(){return"ResourceURL"}};function Yn(e){return e instanceof Ve?e.changingThisBreaksApplicationSecurity:e}function Fa(e,t){let n=yd(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Jr})`)}return n===t}function yd(e){return e instanceof Ve&&e.getTypeName()||null}function Gg(e){return new ea(e)}function zg(e){return new ta(e)}function Qg(e){return new na(e)}function Zg(e){return new ra(e)}function Yg(e){return new oa(e)}function Jg(e){let t=new sa(e);return Kg()?new ia(t):t}var ia=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Yo(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},sa=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Yo(t),n}};function Kg(){try{return!!new window.DOMParser().parseFromString(Yo(""),"text/html")}catch{return!1}}var Xg=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function ja(e){return e=String(e),e.match(Xg)?e:"unsafe:"+e}function He(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Jn(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var vd=He("area,br,col,hr,img,wbr"),Id=He("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Ed=He("rp,rt"),em=Jn(Ed,Id),tm=Jn(Id,He("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),nm=Jn(Ed,He("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),pu=Jn(vd,tm,nm,em),Dd=He("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),rm=He("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),om=He("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),im=Jn(Dd,rm,om),sm=He("script,style,template"),aa=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=lm(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=cm(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=hu(t).toLowerCase();if(!pu.hasOwnProperty(n))return this.sanitizedSomething=!0,!sm.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!im.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Dd[a]&&(c=ja(c)),this.buf.push(" ",s,'="',gu(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=hu(t).toLowerCase();pu.hasOwnProperty(n)&&!vd.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(gu(t))}};function am(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function cm(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw wd(t);return t}function lm(e){let t=e.firstChild;if(t&&am(e,t))throw wd(t);return t}function hu(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function wd(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var um=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,dm=/([^\#-~ |!])/g;function gu(e){return e.replace(/&/g,"&amp;").replace(um,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(dm,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var To;function fm(e,t){let n=null;try{To=To||Jg(e);let r=t?String(t):"";n=To.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=To.getInertBodyElement(r)}while(r!==i);let a=new aa().sanitizeChildren(mu(n)||n);return Yo(a)}finally{if(n){let r=mu(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function mu(e){return"content"in e&&pm(e)?e.content:null}function pm(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Jo=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Jo||{});function Cd(e){let t=Td();return t?t.sanitize(Jo.URL,e)||"":Fa(e,"URL")?Yn(e):ja(bn(e))}function bd(e){let t=Td();if(t)return fu(t.sanitize(Jo.RESOURCE_URL,e)||"");if(Fa(e,"ResourceURL"))return fu(Yn(e));throw new _(904,!1)}function hm(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?bd:Cd}function gm(e,t,n){return hm(t,n)(e)}function Td(){let e=I();return e&&e[be].sanitizer}var mm=/^>|^->|<!--|-->|--!>|<!-$/g,ym=/(<|>)/g,vm="\u200B$1\u200B";function Im(e){return e.replace(mm,t=>t.replace(ym,vm))}function _d(e){return e instanceof Function?e():e}function Em(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Md="ng-template";function Dm(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Em(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Va(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Va(e){return e.type===4&&e.value!==Md}function wm(e,t,n){let r=e.type===4&&!n?Md:e.value;return t===r}function Cm(e,t,n){let r=4,o=e.attrs,i=o!==null?_m(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!me(r)&&!me(c))return!1;if(s&&me(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!wm(e,c,n)||c===""&&t.length===1){if(me(r))return!1;s=!0}}else if(r&8){if(o===null||!Dm(e,o,c,n)){if(me(r))return!1;s=!0}}else{let l=t[++a],u=bm(c,o,Va(e),n);if(u===-1){if(me(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(me(r))return!1;s=!0}}}}return me(r)||s}function me(e){return(e&1)===0}function bm(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Mm(t,e)}function Nd(e,t,n=!1){for(let r=0;r<t.length;r++)if(Cm(e,t[r],n))return!0;return!1}function Tm(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function _m(e){for(let t=0;t<e.length;t++){let n=e[t];if(Gu(n))return t}return e.length}function Mm(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Nm(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function yu(e,t){return e?":not("+t.trim()+")":t}function xm(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!me(s)&&(t+=yu(i,o),o=""),r=s,i=i||!me(r);n++}return o!==""&&(t+=yu(i,o)),t}function Sm(e){return e.map(xm).join(",")}function Rm(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!me(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var le={};function Om(e,t){return e.createText(t)}function Am(e,t,n){e.setValue(t,n)}function km(e,t){return e.createComment(Im(t))}function xd(e,t,n){return e.createElement(t,n)}function Fo(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Sd(e,t,n){e.appendChild(t,n)}function vu(e,t,n,r,o){r!==null?Fo(e,t,n,r,o):Sd(e,t,n)}function Rd(e,t,n){e.removeChild(null,t,n)}function Pm(e,t,n){e.setAttribute(t,"style",n)}function Lm(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Od(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&mg(e,t,r),o!==null&&Lm(e,t,o),i!==null&&Pm(e,t,i)}function Ha(e,t,n,r,o,i,s,a,c,l,u){let d=L+r,p=d+o,f=Fm(d,p),h=typeof l=="function"?l():l;return f[D]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function Fm(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:le);return n}function jm(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Ha(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ba(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[fe]=o,d[y]=r|4|128|8|64|1024,(l!==null||e&&e[y]&2048)&&(d[y]|=2048),ys(d),d[H]=d[wt]=e,d[P]=n,d[be]=s||e&&e[be],d[O]=a||e&&e[O],d[Dt]=c||e&&e[Dt]||null,d[Y]=i,d[_n]=Rg(),d[Et]=u,d[fs]=l,d[J]=t.type==2?e[J]:d,d}function Vm(e,t,n){let r=he(t,e),o=jm(n),i=e[be].rendererFactory,s=$a(e,Ba(e,o,null,Ad(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Ad(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function kd(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function $a(e,t){return e[Wt]?e[ds][ie]=t:e[Wt]=t,e[ds]=t,t}function Hm(e=1){Pd(A(),I(),Le()+e,!1)}function Pd(e,t,n,r){if(!r)if((t[y]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Mo(t,i,n)}else{let i=e.preOrderHooks;i!==null&&No(t,i,0,n)}Xe(n)}var Ko=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ko||{});function ca(e,t,n,r){let o=w(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Ko.SignalBased)!==0&&(c=t[i][V]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Bu(t,c,i,r)}finally{w(o)}}function Ld(e,t,n,r,o){let i=Le(),s=r&2;try{Xe(-1),s&&t.length>L&&Pd(e,t,L,!1),S(s?2:0,o,n),n(r,o)}finally{Xe(i),S(s?3:1,o,n)}}function Xo(e,t,n){Gm(e,t,n),(n.flags&64)===64&&zm(e,t,n)}function Ua(e,t,n=he){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Bm(e,t,n,r){let i=r.get(hd,pd)||n===Kt.ShadowDom,s=e.selectRootElement(t,i);return $m(s),s}function $m(e){Um(e)}var Um=()=>null;function qm(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Fd(e,t,n,r,o,i){let s=t[D];if(Wa(e,s,t,n,r)){Ze(e)&&Wm(t,e.index);return}jd(e,t,n,r,o,i)}function jd(e,t,n,r,o,i){if(e.type&3){let s=he(e,t);n=qm(n),r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function Wm(e,t){let n=ae(t,e);n[y]&16||(n[y]|=64)}function Gm(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Ze(n)&&Vm(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Po(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Hn(t,e,s,n);if(sn(c,t),i!==null&&Jm(t,s-r,c,a,n,i),Me(a)){let l=ae(n.index,t);l[P]=Hn(t,e,s,n)}}}function zm(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Ql();try{Xe(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];mo(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Qm(c,l)}}finally{Xe(-1),mo(s)}}function Qm(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function qa(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Nd(t,i.selectors,!1)&&(r??=[],Me(i)?r.unshift(i):r.push(i))}return r}function Zm(e,t,n,r,o,i){let s=he(e,t);Ym(t[O],s,i,e.value,n,r,o)}function Ym(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?bn(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Jm(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];ca(r,n,c,l)}}function Km(e,t){let n=e[Dt];if(!n)return;n.get(Ne,null)?.(t)}function Wa(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],d=t.data[l];ca(d,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];ca(u,l,r,o),a=!0}return a}function Xm(e,t){let n=ae(t,e),r=n[D];ey(r,n);let o=n[fe];o!==null&&n[Et]===null&&(n[Et]=gd(o,n[Dt])),S(18),Ga(r,n,n[P]),S(19,n[P])}function ey(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Ga(e,t,n){vo(t);try{let r=e.viewQuery;r!==null&&Xs(1,r,n);let o=e.template;o!==null&&Ld(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Te]?.finishViewCreation(e),e.staticContentQueries&&md(e,t),e.staticViewQueries&&Xs(2,e.viewQuery,n);let i=e.components;i!==null&&ty(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[y]&=-5,Io()}}function ty(e,t){for(let n=0;n<t.length;n++)Xm(e,t[n])}function Kn(e,t,n,r){let o=w(null);try{let i=t.tView,a=e[y]&4096?4096:16,c=Ba(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[ze]=l;let u=e[Te];return u!==null&&(c[Te]=u.createEmbeddedView(i)),Ga(i,c,n),c}finally{w(o)}}function Xt(e,t){return!t||t.firstChild===null||od(e)}var Iu=!1,ny=new x(""),ry;function za(e,t){return ry(e,t)}var jo=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(jo||{});function ei(e){return(e.flags&32)===32}function Zt(e,t,n,r,o){if(r!=null){let i,s=!1;pe(r)?i=r:_e(r)&&(s=!0,r=r[fe]);let a=se(r);e===0&&n!==null?o==null?Sd(t,n,a):Fo(t,n,a,o||null,!0):e===1&&n!==null?Fo(t,n,a,o||null,!0):e===2?Rd(t,a,s):e===3&&t.destroyNode(a),i!=null&&py(t,e,i,n,o)}}function oy(e,t){Vd(e,t),t[fe]=null,t[Y]=null}function iy(e,t,n,r,o,i){r[fe]=o,r[Y]=t,ri(e,r,n,1,o,i)}function Vd(e,t){t[be].changeDetectionScheduler?.notify(9),ri(e,t,t[O],2,null,null)}function sy(e){let t=e[Wt];if(!t)return js(e[D],e);for(;t;){let n=null;if(_e(t))n=t[Wt];else{let r=t[$];r&&(n=r)}if(!n){for(;t&&!t[ie]&&t!==e;)_e(t)&&js(t[D],t),t=t[H];t===null&&(t=e),_e(t)&&js(t[D],t),n=t&&t[ie]}t=n}}function Qa(e,t){let n=e[Tt],r=n.indexOf(t);n.splice(r,1)}function ti(e,t){if(zt(t))return;let n=t[O];n.destroyNode&&ri(e,t,n,3,null,null),sy(t)}function js(e,t){if(zt(t))return;let n=w(null);try{t[y]&=-129,t[y]|=256,t[ne]&&kt(t[ne]),cy(e,t),ay(e,t),t[D].type===1&&t[O].destroy();let r=t[ze];if(r!==null&&pe(t[H])){r!==t[H]&&Qa(r,t);let o=t[Te];o!==null&&o.detachView(e)}Js(t)}finally{w(n)}}function ay(e,t){let n=e.cleanup,r=t[qt];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[qt]=null);let o=t[Ae];if(o!==null){t[Ae]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Pe];if(i!==null){t[Pe]=null;for(let s of i)s.destroy()}}function cy(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Rt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];S(4,a,c);try{c.call(a)}finally{S(5,a,c)}}else{S(4,o,i);try{i.call(o)}finally{S(5,o,i)}}}}}function Hd(e,t,n){return ly(e,t.parent,n)}function ly(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[fe];if(Ze(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Kt.None||o===Kt.Emulated)return null}return he(r,n)}function Bd(e,t,n){return dy(e,t,n)}function uy(e,t,n){return e.type&40?he(e,n):null}var dy=uy,Eu;function ni(e,t,n,r){let o=Hd(e,r,t),i=t[O],s=r.parent||t[Y],a=Bd(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)vu(i,o,n[c],a,!1);else vu(i,o,n,a,!1);Eu!==void 0&&Eu(i,r,t,n,o)}function jn(e,t){if(t!==null){let n=t.type;if(n&3)return he(t,e);if(n&4)return la(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return jn(e,r);{let o=e[t.index];return pe(o)?la(-1,o):se(o)}}else{if(n&128)return jn(e,t.next);if(n&32)return za(t,e)()||se(e[t.index]);{let r=$d(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=qe(e[J]);return jn(o,r)}else return jn(e,t.next)}}}return null}function $d(e,t){if(t!==null){let r=e[J][Y],o=t.projection;return r.projection[o]}return null}function la(e,t){let n=$+e+1;if(n<t.length){let r=t[n],o=r[D].firstChild;if(o!==null)return jn(r,o)}return t[Qe]}function Za(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&sn(se(a),r),n.flags|=2),!ei(n))if(c&8)Za(e,t,n.child,r,o,i,!1),Zt(t,e,o,a,i);else if(c&32){let l=za(n,r),u;for(;u=l();)Zt(t,e,o,u,i);Zt(t,e,o,a,i)}else c&16?Ud(e,t,r,n,o,i):Zt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ri(e,t,n,r,o,i){Za(n,r,e.firstChild,t,o,i,!1)}function fy(e,t,n){let r=t[O],o=Hd(e,n,t),i=n.parent||t[Y],s=Bd(i,n,t);Ud(r,0,t,n,o,s)}function Ud(e,t,n,r,o,i){let s=n[J],c=s[Y].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];Zt(t,e,o,u,i)}else{let l=c,u=s[H];od(r)&&(l.flags|=128),Za(e,t,l,u,o,i,!0)}}function py(e,t,n,r,o){let i=n[Qe],s=se(n);i!==s&&Zt(t,e,r,i,o);for(let a=$;a<n.length;a++){let c=n[a];ri(c[D],c,e,t,r,i)}}function hy(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:jo.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=jo.Important),e.setStyle(n,r,o,i))}}function Bn(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(se(i)),pe(i)&&qd(i,r);let s=n.type;if(s&8)Bn(e,t,n.child,r);else if(s&32){let a=za(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=$d(t,n);if(Array.isArray(a))r.push(...a);else{let c=qe(t[J]);Bn(c[D],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function qd(e,t){for(let n=$;n<e.length;n++){let r=e[n],o=r[D].firstChild;o!==null&&Bn(r[D],r,o,t)}e[Qe]!==e[fe]&&t.push(e[Qe])}function Wd(e){if(e[bt]!==null){for(let t of e[bt])t.impl.addSequence(t);e[bt].length=0}}var Gd=[];function gy(e){return e[ne]??my(e)}function my(e){let t=Gd.pop()??Object.create(vy);return t.lView=e,t}function yy(e){e.lView[ne]!==e&&(e.lView=null,Gd.push(e))}var vy=W(q({},Be),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Ye(e.lView)},consumerOnSignalRead(){this.lView[ne]=this}});function Iy(e){let t=e[ne]??Object.create(Ey);return t.lView=e,t}var Ey=W(q({},Be),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=qe(e.lView);for(;t&&!zd(t[D]);)t=qe(t);t&&vs(t)},consumerOnSignalRead(){this.lView[ne]=this}});function zd(e){return e.type!==2}function Qd(e){if(e[Pe]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Pe])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[y]&8192)}}var Dy=100;function Ya(e,t=0){let r=e[be].rendererFactory,o=!1;o||r.begin?.();try{wy(e,t)}finally{o||r.end?.()}}function wy(e,t){let n=Ts();try{Qt(!0),ua(e,t);let r=0;for(;Sn(e);){if(r===Dy)throw new _(103,!1);r++,ua(e,1)}}finally{Qt(n)}}function Zd(e,t){bs(t?Rn.Exhaustive:Rn.OnlyDirtyViews);try{Ya(e)}finally{bs(Rn.Off)}}function Cy(e,t,n,r){if(zt(t))return;let o=t[y],i=!1,s=!1;vo(t);let a=!0,c=null,l=null;i||(zd(e)?(l=gy(t),c=Re(l)):pr()===null?(a=!1,l=Iy(t),c=Re(l)):t[ne]&&(kt(t[ne]),t[ne]=null));try{ys(t),Wl(e.bindingStartIndex),n!==null&&Ld(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let f=e.preOrderCheckHooks;f!==null&&Mo(t,f,null)}else{let f=e.preOrderHooks;f!==null&&No(t,f,0,null),Ls(t,0)}if(s||by(t),Qd(t),Yd(t,0),e.contentQueries!==null&&md(e,t),!i)if(u){let f=e.contentCheckHooks;f!==null&&Mo(t,f)}else{let f=e.contentHooks;f!==null&&No(t,f,1),Ls(t,1)}_y(e,t);let d=e.components;d!==null&&Kd(t,d,0);let p=e.viewQuery;if(p!==null&&Xs(2,p,r),!i)if(u){let f=e.viewCheckHooks;f!==null&&Mo(t,f)}else{let f=e.viewHooks;f!==null&&No(t,f,2),Ls(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[ao]){for(let f of t[ao])f();t[ao]=null}i||(Wd(t),t[y]&=-73)}catch(u){throw i||Ye(t),u}finally{l!==null&&($e(l,c),a&&yy(l)),Io()}}function Yd(e,t){for(let n=sd(e);n!==null;n=ad(n))for(let r=$;r<n.length;r++){let o=n[r];Jd(o,t)}}function by(e){for(let t=sd(e);t!==null;t=ad(t)){if(!(t[y]&2))continue;let n=t[Tt];for(let r=0;r<n.length;r++){let o=n[r];vs(o)}}}function Ty(e,t,n){S(18);let r=ae(t,e);Jd(r,n),S(19,r[P])}function Jd(e,t){uo(e)&&ua(e,t)}function ua(e,t){let r=e[D],o=e[y],i=e[ne],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&ot(i)),s||=!1,i&&(i.dirty=!1),e[y]&=-9217,s)Cy(r,e,r.template,e[P]);else if(o&8192){Qd(e),Yd(e,1);let a=r.components;a!==null&&Kd(e,a,1),Wd(e)}}function Kd(e,t,n){for(let r=0;r<t.length;r++)Ty(e,t[r],n)}function _y(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Xe(~o);else{let i=o,s=n[++r],a=n[++r];zl(s,i);let c=t[i];S(24,c),a(2,c),S(25,c)}}}finally{Xe(-1)}}function Ja(e,t){let n=Ts()?64:1088;for(e[be].changeDetectionScheduler?.notify(t);e;){e[y]|=n;let r=qe(e);if(Gt(e)&&!r)return e;e=r}return null}function Xd(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function ef(e,t){let n=$+t;if(n<e.length)return e[n]}function Xn(e,t,n,r=!0){let o=t[D];if(My(o,t,e,n),r){let s=la(n,e),a=t[O],c=a.parentNode(e[Qe]);c!==null&&iy(o,e[Y],a,t,c,s)}let i=t[Et];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function tf(e,t){let n=$n(e,t);return n!==void 0&&ti(n[D],n),n}function $n(e,t){if(e.length<=$)return;let n=$+t,r=e[n];if(r){let o=r[ze];o!==null&&o!==e&&Qa(o,r),t>0&&(e[n-1][ie]=r[ie]);let i=Tn(e,$+t);oy(r[D],r);let s=i[Te];s!==null&&s.detachView(i[D]),r[H]=null,r[ie]=null,r[y]&=-129}return r}function My(e,t,n,r){let o=$+r,i=n.length;r>0&&(n[o-1][ie]=t),r<i-$?(t[ie]=n[o],es(n,$+r,t)):(n.push(t),t[ie]=null),t[H]=n;let s=t[ze];s!==null&&n!==s&&nf(s,t);let a=t[Te];a!==null&&a.insertView(e),fo(t),t[y]|=128}function nf(e,t){let n=e[Tt],r=t[H];if(_e(r))e[y]|=2;else{let o=r[H][J];t[J]!==o&&(e[y]|=2)}n===null?e[Tt]=[t]:n.push(t)}var et=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[D];return Bn(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[P]}set context(t){this._lView[P]=t}get destroyed(){return zt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[H];if(pe(t)){let n=t[Mn],r=n?n.indexOf(this):-1;r>-1&&($n(t,r),Tn(n,r))}this._attachedToViewContainer=!1}ti(this._lView[D],this._lView)}onDestroy(t){Is(this._lView,t)}markForCheck(){Ja(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[y]&=-129}reattach(){fo(this._lView),this._lView[y]|=128}detectChanges(){this._lView[y]|=1024,Ya(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[Dt].get(ny,Iu)}catch{this.exhaustive=Iu}}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Gt(this._lView),n=this._lView[ze];n!==null&&!t&&Qa(n,this._lView),Vd(this._lView[D],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t;let n=Gt(this._lView),r=this._lView[ze];r!==null&&!n&&nf(r,this._lView),fo(this._lView)}};var Un=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=Ny;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=Kn(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new et(i)}}return e})();function Ny(){return oi(U(),I())}function oi(e,t){return e.type&4?new Un(t,e,on(e,t)):null}function er(e,t,n,r,o){let i=e.data[t];if(i===null)i=xy(e,t,n,r,o),Gl()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Ul();i.injectorIndex=s===null?-1:s.injectorIndex}return Je(i,!0),i}function xy(e,t,n,r,o){let i=Cs(),s=ho(),a=s?i:i&&i.parent,c=e.data[t]=Ry(e,a,n,t,r,o);return Sy(e,c,i,s),c}function Sy(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Ry(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return ws()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var ux=new RegExp(`^(\\d+)*(${Hg}|${Vg})*(.*)`);function Oy(e){let t=e[ps]??[],r=e[H][O],o=[];for(let i of t)i.data[fd]!==void 0?o.push(i):Ay(i,r);e[ps]=o}function Ay(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[dd];for(;n<o;){let i=r.nextSibling;Rd(t,r,!1),r=i,n++}}}var ky=()=>null,Py=()=>null;function Vo(e,t){return ky(e,t)}function rf(e,t,n){return Py(e,t,n)}var of=class{},ii=class{},da=class{resolveComponentFactory(t){throw new _(917,!1)}},tr=class{static NULL=new da},qn=class{},Ly=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Fy()}return e})();function Fy(){let e=I(),t=U(),n=ae(t.index,e);return(_e(n)?n:e)[O]}var sf=(()=>{class e{static \u0275prov=B({token:e,providedIn:"root",factory:()=>null})}return e})();var So={},fa=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,So,r);return o!==So||n===So?o:this.parentInjector.get(t,n,r)}};function pa(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Xr(o,a);else if(i==2){let c=a,l=t[++s];r=Xr(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function nr(e,t=0){let n=I();if(n===null)return De(e,t);let r=U();return Xu(r,n,G(e),t)}function jy(){let e="invalid";throw new Error(e)}function Ka(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,l=null;for(let u of s)if(u.resolveHostDirectives!==null){[a,c,l]=u.resolveHostDirectives(s);break}By(e,t,n,a,i,c,l)}i!==null&&r!==null&&Vy(n,r,i)}function Vy(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}function Hy(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function By(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&Me(f)&&(c=!0,Hy(e,n,p)),Zs(Po(n,t),e,f.type)}zy(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,u=!1,d=kd(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=Jt(n.mergedAttrs,f.hostAttrs),Uy(e,n,t,d,f),Gy(d,f,o),s!==null&&s.has(f)){let[g,N]=s.get(f);n.directiveToIndex.set(f.type,[d,g+n.directiveStart,N+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let h=f.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),d++}$y(e,n,i)}function $y(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Du(0,t,o,r),Du(1,t,o,r),Cu(t,r,!1);else{let i=n.get(o);wu(0,t,i,r),wu(1,t,i,r),Cu(t,r,!0)}}}function Du(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),af(t,i)}}function wu(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),af(t,s)}}function af(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Cu(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Va(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Uy(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Ue(o.type,!0)),s=new Rt(i,Me(o),nr);e.blueprint[r]=s,n[r]=s,qy(e,t,r,kd(e,n,o.hostVars,le),o)}function qy(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Wy(s)!=a&&s.push(a),s.push(n,r,i)}}function Wy(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Gy(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Me(t)&&(n[""]=e)}}function zy(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function cf(e,t,n,r,o,i,s,a){let c=t.consts,l=ge(c,s),u=er(t,e,2,r,l);return i&&Ka(t,n,u,ge(c,a),o),u.mergedAttrs=Jt(u.mergedAttrs,u.attrs),u.attrs!==null&&pa(u,u.attrs,!1),u.mergedAttrs!==null&&pa(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function lf(e,t){Aa(e,t),co(t)&&e.queries.elementEnd(t)}function Xa(e){return df(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function uf(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function df(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function ff(e,t,n){return e[t]=n}function ce(e,t,n){if(n===le)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Qy(e,t,n,r){let o=ce(e,t,n);return ce(e,t+1,r)||o}function Vs(e,t,n){return function r(o){let i=Ze(e)?ae(e.index,t):t;Ja(i,5);let s=t[P],a=bu(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=bu(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function bu(e,t,n,r){let o=w(null);try{return S(6,t,n),n(r)!==!1}catch(i){return Km(e,i),!1}finally{S(7,t,n),w(o)}}function Zy(e,t,n,r,o,i,s,a){let c=Nn(e),l=!1,u=null;if(!r&&c&&(u=Yy(t,n,i,e.index)),u!==null){let d=u.__ngLastListenerFn__||u;d.__ngNextListenerFn__=s,u.__ngLastListenerFn__=s,l=!0}else{let d=he(e,n),p=r?r(d):d;$g(n,p,i,a);let f=o.listen(p,i,a),h=r?g=>r(se(g[e.index])):e.index;pf(h,t,n,i,a,f,!1)}return l}function Yy(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[qt],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function pf(e,t,n,r,o,i,s){let a=t.firstCreatePass?Ds(t):null,c=Es(n),l=c.length;c.push(o,i),a&&a.push(r,e,l,(l+1)*(s?-1:1))}function Tu(e,t,n,r,o,i){let s=t[n],a=t[D],l=a.data[n].outputs[r],d=s[l].subscribe(i);pf(e.index,a,t,o,i,d,!0)}var ha=Symbol("BINDING");var Ho=class extends tr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Ce(t);return new tt(n,this.ngModule)}};function Jy(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Ko.SignalBased)!==0};return o&&(i.transform=o),i})}function Ky(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function Xy(e,t,n){let r=t instanceof oe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new fa(n,r):n}function ev(e){let t=e.get(qn,null);if(t===null)throw new _(407,!1);let n=e.get(sf,null),r=e.get(te,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function tv(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return xd(t,n,n==="svg"?hs:n==="math"?Sl:null)}var tt=class extends ii{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Jy(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Ky(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Sm(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){S(22);let a=w(null);try{let c=this.componentDef,l=nv(r,c,s,i),u=Xy(c,o||this.ngModule,t),d=ev(u),p=d.rendererFactory.createRenderer(null,c),f=r?Bm(p,r,c.encapsulation,u):tv(c,p),h=s?.some(_u)||i?.some(b=>typeof b!="function"&&b.bindings.some(_u)),g=Ba(null,l,null,512|Ad(c),null,null,d,p,u,null,gd(f,u,!0));g[L]=f,vo(g);let N=null;try{let b=cf(L,l,g,"#host",()=>l.directiveRegistry,!0,0);f&&(Od(p,f,b),sn(f,g)),Xo(l,g,b),La(l,b,g),lf(l,b),n!==void 0&&ov(b,this.ngContentSelectors,n),N=ae(b.index,g),g[P]=N[P],Ga(l,g,null)}catch(b){throw N!==null&&Js(N),Js(g),b}finally{S(23),Io()}return new Bo(this.componentType,g,!!h)}finally{w(a)}}};function nv(e,t,n,r){let o=e?["ng-version","20.0.0"]:Rm(t.selectors[0]),i=null,s=null,a=0;if(n)for(let u of n)a+=u[ha].requiredVars,u.create&&(u.targetIdx=0,(i??=[]).push(u)),u.update&&(u.targetIdx=0,(s??=[]).push(u));if(r)for(let u=0;u<r.length;u++){let d=r[u];if(typeof d!="function")for(let p of d.bindings){a+=p[ha].requiredVars;let f=u+1;p.create&&(p.targetIdx=f,(i??=[]).push(p)),p.update&&(p.targetIdx=f,(s??=[]).push(p))}}let c=[t];if(r)for(let u of r){let d=typeof u=="function"?u:u.type,p=os(d);c.push(p)}return Ha(0,null,rv(i,s),1,a,c,null,null,null,[o],null)}function rv(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function _u(e){let t=e[ha].kind;return t==="input"||t==="twoWay"}var Bo=class extends of{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=xn(n[D],L),this.location=on(this._tNode,n),this.instance=ae(this._tNode.index,n)[P],this.hostView=this.changeDetectorRef=new et(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Wa(r,o[D],o,t,n);this.previousInputValues.set(t,n);let s=ae(r.index,o);Ja(s,1)}get injector(){return new St(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function ov(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var si=(()=>{class e{static __NG_ELEMENT_ID__=iv}return e})();function iv(){let e=U();return gf(e,I())}var sv=si,hf=class extends sv{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return on(this._hostTNode,this._hostLView)}get injector(){return new St(this._hostTNode,this._hostLView)}get parentInjector(){let t=ka(this._hostTNode,this._hostLView);if(zu(t)){let n=Ao(t,this._hostLView),r=Oo(t),o=n[D].data[r+8];return new St(o,n)}else return new St(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Mu(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-$}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Vo(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Xt(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!ag(t),l;if(c)l=n;else{let N=n||{};l=N.index,r=N.injector,o=N.projectableNodes,i=N.environmentInjector||N.ngModuleRef,s=N.directives,a=N.bindings}let u=c?t:new tt(Ce(t)),d=r||this.parentInjector;if(!i&&u.ngModule==null){let b=(c?d:this.parentInjector).get(oe,null);b&&(i=b)}let p=Ce(u.componentType??{}),f=Vo(this._lContainer,p?.id??null),h=f?.firstChild??null,g=u.create(d,o,h,i,s,a);return this.insertImpl(g.hostView,l,Xt(this._hostTNode,f)),g}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Ol(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[H],l=new hf(c,c[Y],c[H]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Xn(s,o,i,r),t.attachToViewContainerRef(),es(Hs(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Mu(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=$n(this._lContainer,n);r&&(Tn(Hs(this._lContainer),n),ti(r[D],r))}detach(t){let n=this._adjustIndex(t,-1),r=$n(this._lContainer,n);return r&&Tn(Hs(this._lContainer),n)!=null?new et(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Mu(e){return e[Mn]}function Hs(e){return e[Mn]||(e[Mn]=[])}function gf(e,t){let n,r=t[e.index];return pe(r)?n=r:(n=Xd(r,t,null,e),t[e.index]=n,$a(t,n)),cv(n,t,e,r),new hf(n,e,t)}function av(e,t){let n=e[O],r=n.createComment(""),o=he(t,e),i=n.parentNode(o);return Fo(n,i,r,n.nextSibling(o),!1),r}var cv=dv,lv=()=>!1;function uv(e,t,n){return lv(e,t,n)}function dv(e,t,n,r){if(e[Qe])return;let o;n.type&8?o=se(r):o=av(t,n),e[Qe]=o}var ga=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},ma=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)tc(t,n).matches!==null&&this.queries[n].setDirty()}},$o=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=mv(t):this.predicate=t}},ya=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},va=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,fv(n,i)),this.matchTNodeWithReadOption(t,n,xo(n,t,i,!1,!1))}else r===Un?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,xo(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Zn||o===si||o===Un&&n.type&4)this.addMatch(n.index,-2);else{let i=xo(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function fv(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function pv(e,t){return e.type&11?on(e,t):e.type&4?oi(e,t):null}function hv(e,t,n,r){return n===-1?pv(t,e):n===-2?gv(e,t,r):Hn(e,e[D],n,t)}function gv(e,t,n){if(n===Zn)return on(t,e);if(n===Un)return oi(t,e);if(n===si)return gf(t,e)}function mf(e,t,n,r){let o=t[Te].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(hv(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Ia(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=mf(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let d=$;d<u.length;d++){let p=u[d];p[ze]===p[H]&&Ia(p[D],p,l,r)}if(u[Tt]!==null){let d=u[Tt];for(let p=0;p<d.length;p++){let f=d[p];Ia(f[D],f,l,r)}}}}}return r}function ec(e,t){return e[Te].queries[t].queryList}function yf(e,t,n){let r=new Lo((n&4)===4);return Pl(e,t,r,r.destroy),(t[Te]??=new ma).queries.push(new ga(r))-1}function vf(e,t,n){let r=A();return r.firstCreatePass&&(Ef(r,new $o(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),yf(r,I(),t)}function If(e,t,n,r){let o=A();if(o.firstCreatePass){let i=U();Ef(o,new $o(t,n,r),i.index),yv(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return yf(o,I(),n)}function mv(e){return e.split(",").map(t=>t.trim())}function Ef(e,t,n){e.queries===null&&(e.queries=new ya),e.queries.track(new va(t,n))}function yv(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function tc(e,t){return e.queries.getByIndex(t)}function Df(e,t){let n=e[D],r=tc(n,t);return r.crossesNgTemplate?Ia(n,e,t,[]):mf(n,e,r,t)}function wf(e,t,n){let r,o=un(()=>{r._dirtyCounter();let i=vv(r,e);if(t&&i===void 0)throw new _(-951,!1);return i});return r=o[V],r._dirtyCounter=Eo(0),r._flatValue=void 0,o}function nc(e){return wf(!0,!1,e)}function rc(e){return wf(!0,!0,e)}function Cf(e,t){let n=e[V];n._lView=I(),n._queryIndex=t,n._queryList=ec(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function vv(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[y]&4)return t?void 0:z;let o=ec(n,r),i=Df(n,r);return o.reset(i,rd),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}var Nu=new Set;function nt(e){Nu.has(e)||(Nu.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var en=class{},bf=class{};var Uo=class extends en{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ho(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=rs(t);this._bootstrapComponents=_d(i.bootstrap),this._r3Injector=Ss(t,n,[{provide:en,useValue:this},{provide:tr,useValue:this.componentFactoryResolver},...r],ee(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},qo=class extends bf{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Uo(this.moduleType,t,[])}};var Wn=class extends en{injector;componentFactoryResolver=new Ho(this);instance=null;constructor(t){super();let n=new vt([...t.providers,{provide:en,useValue:this},{provide:tr,useValue:this.componentFactoryResolver}],t.parent||Ut(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Tf(e,t,n=null){return new Wn({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Iv=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=is(!1,n.type),o=r.length>0?Tf([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=B({token:e,providedIn:"environment",factory:()=>new e(De(oe))})}return e})();function Ev(e){return rn(()=>{let t=_f(e),n=W(q({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Pa.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Iv).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Kt.Emulated,styles:e.styles||z,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&nt("NgStandalone"),Mf(n);let r=e.dependencies;return n.directiveDefs=xu(r,!1),n.pipeDefs=xu(r,!0),n.id=Nv(n),n})}function Dv(e){return Ce(e)||os(e)}function wv(e){return e!==null}function Cv(e){return rn(()=>({type:e.type,bootstrap:e.bootstrap||z,declarations:e.declarations||z,imports:e.imports||z,exports:e.exports||z,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function bv(e,t){if(e==null)return We;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Ko.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Tv(e){if(e==null)return We;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function _v(e){return rn(()=>{let t=_f(e);return Mf(t),t})}function Mv(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function _f(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||We,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||z,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:bv(e.inputs,t),outputs:Tv(e.outputs),debugInfo:null}}function Mf(e){e.features?.forEach(t=>t(e))}function xu(e,t){if(!e)return null;let n=t?Cl:Dv;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(wv)}function Nv(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function xv(e){return Object.getPrototypeOf(e.prototype).constructor}function Nf(e){let t=xv(e.type),n=!0,r=[e];for(;t;){let o;if(Me(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Bs(e.inputs),s.declaredInputs=Bs(e.declaredInputs),s.outputs=Bs(e.outputs);let a=o.hostBindings;a&&kv(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&Ov(e,c),l&&Av(e,l),Sv(e,o),hl(e.outputs,o.outputs),Me(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Nf&&(n=!1)}}t=Object.getPrototypeOf(t)}Rv(r)}function Sv(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Rv(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Jt(o.hostAttrs,n=Jt(n,o.hostAttrs))}}function Bs(e){return e===We?{}:e===z?[]:e}function Ov(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Av(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function kv(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Pv(e,t,n,r,o,i,s,a,c){let l=t.consts,u=er(t,e,4,s||null,a||null);po()&&Ka(t,n,u,ge(l,c),qa),u.mergedAttrs=Jt(u.mergedAttrs,u.attrs),Aa(t,u);let d=u.tView=Ha(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function tn(e,t,n,r,o,i,s,a,c,l,u){let d=n+L,p=t.firstCreatePass?Pv(d,t,e,r,o,i,s,a,l):t.data[d];c&&(p.flags|=c),Je(p,!1);let f=Lv(t,e,p,n);kn()&&ni(t,e,f,p),sn(f,e);let h=Xd(f,e,f,p);return e[d]=h,$a(e,h),uv(h,p,e),Nn(p)&&Xo(t,e,p),l!=null&&Ua(e,p,u),p}function xf(e,t,n,r,o,i,s,a){let c=I(),l=A(),u=ge(l.consts,i);return tn(c,l,e,t,n,r,o,u,void 0,s,a),xf}var Lv=Fv;function Fv(e,t,n,r){return Pn(!0),t[O].createComment("")}var ai=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(ai||{}),an=new x(""),Sf=!1,Ea=class extends re{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,ls()&&(this.destroyRef=m(Fe,{optional:!0})??void 0,this.pendingTasks=m(Mt,{optional:!0})??void 0)}emit(t){let n=w(null);try{super.next(t)}finally{w(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof F&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},je=Ea;function Rf(e){let t,n;function r(){e=Nt;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Su(e){return queueMicrotask(()=>e()),()=>{e=Nt}}var oc="isAngularZone",Wo=oc+"_ID",jv=0,K=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new je(!1);onMicrotaskEmpty=new je(!1);onStable=new je(!1);onError=new je(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Sf}=t;if(typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Bv(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(oc)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Vv,Nt,Nt);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Vv={};function ic(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Hv(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Rf(()=>{e.callbackScheduled=!1,Da(e),e.isCheckStableRunning=!0,ic(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Da(e)}function Bv(e){let t=()=>{Hv(e)},n=jv++;e._inner=e._inner.fork({name:"angular",properties:{[oc]:!0,[Wo]:n,[Wo+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if($v(c))return r.invokeTask(i,s,a,c);try{return Ru(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Ou(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Ru(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Uv(c)&&t(),Ou(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Da(e),ic(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Da(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Ru(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Ou(e){e._nesting--,ic(e)}var Gn=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new je;onMicrotaskEmpty=new je;onStable=new je;onError=new je;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function $v(e){return Of(e,"__ignore_ng_zone__")}function Uv(e){return Of(e,"__scheduler_tick__")}function Of(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var ci=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})(),sc=[0,1,2,3],ac=(()=>{class e{ngZone=m(K);scheduler=m(te);errorHandler=m(ke,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){m(an,{optional:!0})}execute(){let n=this.sequences.size>0;n&&S(16),this.executing=!0;for(let r of sc)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&S(17)}register(n){let{view:r}=n;r!==void 0?((r[bt]??=[]).push(n),Ye(r),r[y]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(ai.AFTER_NEXT_RENDER,n):n()}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})(),zn=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[bt];t&&(this.view[bt]=t.filter(n=>n!==this))}};function Af(e,t){!t?.injector&&us(Af);let n=t?.injector??m(de);return nt("NgAfterNextRender"),Wv(e,n,t,!0)}function qv(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Wv(e,t,n,r){let o=t.get(ci);o.impl??=t.get(ac);let i=t.get(an,null,{optional:!0}),s=n?.manualCleanup!==!0?t.get(Fe):null,a=t.get(_t,null,{optional:!0}),c=new zn(o.impl,qv(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var Gv=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var kf=new x("");function cc(e){return!!e&&typeof e.then=="function"}function Pf(e){return!!e&&typeof e.subscribe=="function"}var lc=new x("");function zv(e){return $t([{provide:lc,multi:!0,useValue:e}])}var uc=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=m(lc,{optional:!0})??[];injector=m(de);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=so(this.injector,o);if(cc(i))n.push(i);else if(Pf(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Lf=new x("");function Ff(){mi(()=>{let e="";throw new _(600,e)})}function jf(e){return e.isBoundToModule}var Qv=10;var rr=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=m(Ne);afterRenderManager=m(ci);zonelessEnabled=m(Ln);rootEffectScheduler=m(Fn);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new re;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=m(Mt);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(ft(n=>!n))}constructor(){m(an,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=m(oe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=de.NULL){return this._injector.get(K).run(()=>{S(10);let s=n instanceof ii;if(!this._injector.get(uc).done){let h="";throw new _(405,h)}let c;s?c=n:c=this._injector.get(tr).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=jf(c)?void 0:this._injector.get(en),u=r||c.selector,d=c.create(o,[],u,l),p=d.location.nativeElement,f=d.injector.get(kf,null);return f?.registerApplication(p),d.onDestroy(()=>{this.detachView(d.hostView),Vn(this.components,d),f?.unregisterApplication(p)}),this._loadComponent(d),S(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){S(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(ai.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new _(101,!1);let n=w(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,w(n),this.afterTick.next(),S(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(qn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Qv;)S(14),this.synchronizeOnce(),S(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!Sn(o))continue;let i=r&&!this.zonelessEnabled?0:1;Ya(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Sn(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Vn(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(Lf,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Vn(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Vn(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Vf(e,t,n,r){let o=I(),i=Ke();if(ce(o,i,t)){let s=A(),a=An();Zm(a,o,e,t,n,r)}return Vf}var wa=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function $s(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function Zv(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],d=$s(i,l,i,u,n);if(d!==0){d<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),f=t[c],h=$s(s,p,c,f,n);if(h!==0){h<0&&e.updateValue(s,f),s--,c--;continue}let g=n(i,l),N=n(s,p),b=n(i,u);if(Object.is(b,N)){let ve=n(c,f);Object.is(ve,g)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new Go,o??=ku(e,i,s,n),Ca(e,r,i,b))e.updateValue(i,u),i++,s++;else if(o.has(b))r.set(g,e.detach(i)),s--;else{let ve=e.create(i,t[i]);e.attach(i,ve),i++,s++}}for(;i<=c;)Au(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),d=l.value,p=$s(i,u,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,l=c.next();else{r??=new Go,o??=ku(e,i,s,n);let f=n(i,d);if(Ca(e,r,i,f))e.updateValue(i,d),i++,s++,l=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,l=c.next();else{let h=n(i,u);r.set(h,e.detach(i)),s--}}}for(;!l.done;)Au(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function Ca(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Au(e,t,n,r,o){if(Ca(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function ku(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Go=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function Yv(e,t,n,r,o,i,s,a){nt("NgControlFlow");let c=I(),l=A(),u=ge(l.consts,i);return tn(c,l,e,t,n,r,o,u,256,s,a),dc}function dc(e,t,n,r,o,i,s,a){nt("NgControlFlow");let c=I(),l=A(),u=ge(l.consts,i);return tn(c,l,e,t,n,r,o,u,512,s,a),dc}function Jv(e,t){nt("NgControlFlow");let n=I(),r=Ke(),o=n[r]!==le?n[r]:-1,i=o!==-1?zo(n,L+o):void 0,s=0;if(ce(n,r,e)){let a=w(null);try{if(i!==void 0&&tf(i,s),e!==-1){let c=L+e,l=zo(n,c),u=Ma(n[D],c),d=rf(l,u,n),p=Kn(n,u,t,{dehydratedView:d});Xn(l,p,s,Xt(u,d))}}finally{w(a)}}else if(i!==void 0){let a=ef(i,s);a!==void 0&&(a[P]=t)}}var ba=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-$}};function Kv(e,t){return t}var Ta=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function Xv(e,t,n,r,o,i,s,a,c,l,u,d,p){nt("NgControlFlow");let f=I(),h=A(),g=c!==void 0,N=I(),b=a?s.bind(N[J][P]):s,ve=new Ta(g,b);N[L+e]=ve,tn(f,h,e+1,t,n,r,o,ge(h.consts,i),256),g&&tn(f,h,e+2,c,l,u,d,ge(h.consts,p),512)}var _a=class extends wa{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-$}at(t){return this.getLView(t)[P].$implicit}attach(t,n){let r=n[Et];this.needsIndexUpdate||=t!==this.length,Xn(this.lContainer,n,t,Xt(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,tI(this.lContainer,t)}create(t,n){let r=Vo(this.lContainer,this.templateTNode.tView.ssrId),o=Kn(this.hostLView,this.templateTNode,new ba(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){ti(t[D],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[P].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[P].$index=t}getLView(t){return nI(this.lContainer,t)}};function eI(e){let t=w(null),n=Le();try{let r=I(),o=r[D],i=r[n],s=n+1,a=zo(r,s);if(i.liveCollection===void 0){let l=Ma(o,s);i.liveCollection=new _a(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(Zv(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=Ke(),u=c.length===0;if(ce(r,l,u)){let d=n+2,p=zo(r,d);if(u){let f=Ma(o,d),h=rf(p,f,r),g=Kn(r,f,void 0,{dehydratedView:h});Xn(p,g,0,Xt(f,h))}else o.firstUpdatePass&&Oy(p),tf(p,0)}}}finally{w(t)}}function zo(e,t){return e[t]}function tI(e,t){return $n(e,t)}function nI(e,t){return ef(e,t)}function Ma(e,t){return xn(e,t)}function Hf(e,t,n){let r=I(),o=Ke();if(ce(r,o,t)){let i=A(),s=An();Fd(s,r,e,t,r[O],n)}return Hf}function Na(e,t,n,r,o){Wa(t,e,n,o?"class":"style",r)}function fc(e,t,n,r){let o=I(),i=A(),s=L+e,a=o[O],c=i.firstCreatePass?cf(s,i,o,t,qa,po(),n,r):i.data[s],l=rI(i,o,c,a,t,e);o[s]=l;let u=Nn(c);return Je(c,!0),Od(a,l,c),!ei(c)&&kn()&&ni(i,o,l,c),(Ll()===0||u)&&sn(l,o),Fl(),u&&(Xo(i,o,c),La(i,c,o)),r!==null&&Ua(o,c),fc}function pc(){let e=U();ho()?go():(e=e.parent,Je(e,!1));let t=e;Vl(t)&&Hl(),jl();let n=A();return n.firstCreatePass&&lf(n,t),t.classesWithoutHost!=null&&hg(t)&&Na(n,t,I(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&gg(t)&&Na(n,t,I(),t.stylesWithoutHost,!1),pc}function Bf(e,t,n,r){return fc(e,t,n,r),pc(),Bf}var rI=(e,t,n,r,o,i)=>(Pn(!0),xd(r,o,nu()));function oI(e,t,n,r,o){let i=t.consts,s=ge(i,r),a=er(t,e,8,"ng-container",s);s!==null&&pa(a,s,!0);let c=ge(i,o);return po()&&Ka(t,n,a,c,qa),a.mergedAttrs=Jt(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function hc(e,t,n){let r=I(),o=A(),i=e+L,s=o.firstCreatePass?oI(i,o,r,t,n):o.data[i];Je(s,!0);let a=iI(o,r,s,e);return r[i]=a,kn()&&ni(o,r,a,s),sn(a,r),Nn(s)&&(Xo(o,r,s),La(o,s,r)),n!=null&&Ua(r,s),hc}function gc(){let e=U(),t=A();return ho()?go():(e=e.parent,Je(e,!1)),t.firstCreatePass&&(Aa(t,e),co(e)&&t.queries.elementEnd(e)),gc}function $f(e,t,n){return hc(e,t,n),gc(),$f}var iI=(e,t,n,r)=>(Pn(!0),km(t[O],""));function sI(){return I()}function Uf(e,t,n){let r=I(),o=Ke();if(ce(r,o,t)){let i=A(),s=An();jd(s,r,e,t,r[O],n)}return Uf}var xt=void 0;function aI(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var cI=["en",[["a","p"],["AM","PM"],xt],[["AM","PM"],xt,xt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],xt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],xt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",xt,"{1} 'at' {0}",xt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",aI],Us={};function lI(e){let t=uI(e),n=Pu(t);if(n)return n;let r=t.split("-")[0];if(n=Pu(r),n)return n;if(r==="en")return cI;throw new _(701,!1)}function Pu(e){return e in Us||(Us[e]=we.ng&&we.ng.common&&we.ng.common.locales&&we.ng.common.locales[e]),Us[e]}var qf=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(qf||{});function uI(e){return e.toLowerCase().replace(/_/g,"-")}var or="en-US";var dI=or;function Wf(e){typeof e=="string"&&(dI=e.toLowerCase().replace(/_/g,"-"))}function Gf(e,t,n){let r=I(),o=A(),i=U();return zf(o,r,r[O],i,e,t,n),Gf}function zf(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Vs(r,t,i),Zy(r,e,t,s,n,o,i,c)&&(a=!1)),a){let l=r.outputs?.[o],u=r.hostDirectiveOutputs?.[o];if(u&&u.length)for(let d=0;d<u.length;d+=2){let p=u[d],f=u[d+1];c??=Vs(r,t,i),Tu(r,t,p,f,o,c)}if(l&&l.length)for(let d of l)c??=Vs(r,t,i),Tu(r,t,d,o,o,c)}}function fI(e=1){return Xl(e)}function pI(e,t){let n=null,r=Tm(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Nd(e,i,!0):Nm(r,i))return o}return n}function hI(e){let t=I()[J][Y];if(!t.projection){let n=e?e.length:1,r=t.projection=Dl(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?pI(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function gI(e,t=0,n,r,o,i){let s=I(),a=A(),c=r?e+1:null;c!==null&&tn(s,a,c,r,o,i,null,n);let l=er(a,L+e,16,null,n||null);l.projection===null&&(l.projection=t),go();let d=!s[Et]||ws();s[J][Y].projection[l.projection]===null&&c!==null?mI(s,a,c):d&&!ei(l)&&fy(a,s,l)}function mI(e,t,n){let r=L+n,o=t.data[r],i=e[r],s=Vo(i,o.tView.ssrId),a=Kn(e,o,void 0,{dehydratedView:s});Xn(i,a,0,Xt(o,s))}function yI(e,t,n,r){If(e,t,n,r)}function vI(e,t,n){vf(e,t,n)}function II(e){let t=I(),n=A(),r=yo();On(r+1);let o=tc(n,r);if(e.dirty&&Rl(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Df(t,r);e.reset(i,rd),e.notifyOnChanges()}return!0}return!1}function EI(){return ec(I(),yo())}function DI(e,t,n,r,o){Cf(t,If(e,n,r,o))}function wI(e,t,n,r){Cf(e,vf(t,n,r))}function CI(e=1){On(yo()+e)}function bI(e){let t=ql();return lo(t,L+e)}function _o(e,t){return e<<17|t<<2}function Ot(e){return e>>17&32767}function TI(e){return(e&2)==2}function _I(e,t){return e&131071|t<<17}function xa(e){return e|2}function nn(e){return(e&131068)>>2}function qs(e,t){return e&-131069|t<<2}function MI(e){return(e&1)===1}function Sa(e){return e|1}function NI(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Ot(s),c=nn(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Bt(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=Ot(e[a+1]);e[r+1]=_o(p,a),p!==0&&(e[p+1]=qs(e[p+1],r)),e[a+1]=_I(e[a+1],r)}else e[r+1]=_o(a,0),a!==0&&(e[a+1]=qs(e[a+1],r)),a=r;else e[r+1]=_o(c,0),a===0?a=r:e[c+1]=qs(e[c+1],r),c=r;l&&(e[r+1]=xa(e[r+1])),Lu(e,u,r,!0),Lu(e,u,r,!1),xI(t,u,e,r,i),s=_o(a,c),i?t.classBindings=s:t.styleBindings=s}function xI(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Bt(i,t)>=0&&(n[r+1]=Sa(n[r+1]))}function Lu(e,t,n,r){let o=e[n+1],i=t===null,s=r?Ot(o):nn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];SI(c,t)&&(a=!0,e[s+1]=r?Sa(l):xa(l)),s=r?Ot(l):nn(l)}a&&(e[n+1]=r?xa(o):Sa(o))}function SI(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Bt(e,t)>=0:!1}var ye={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function RI(e){return e.substring(ye.key,ye.keyEnd)}function OI(e){return AI(e),Qf(e,Zf(e,0,ye.textEnd))}function Qf(e,t){let n=ye.textEnd;return n===t?-1:(t=ye.keyEnd=kI(e,ye.key=t,n),Zf(e,t,n))}function AI(e){ye.key=0,ye.keyEnd=0,ye.value=0,ye.valueEnd=0,ye.textEnd=e.length}function Zf(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function kI(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function Yf(e,t,n){return Kf(e,t,n,!1),Yf}function Jf(e,t){return Kf(e,t,null,!0),Jf}function PI(e){FI(UI,LI,e,!0)}function LI(e,t){for(let n=OI(t);n>=0;n=Qf(t,n))oo(e,RI(t),!0)}function Kf(e,t,n,r){let o=I(),i=A(),s=Ms(2);if(i.firstUpdatePass&&ep(i,e,s,r),t!==le&&ce(o,s,t)){let a=i.data[Le()];tp(i,a,o,o[O],e,o[s+1]=WI(t,n),r,s)}}function FI(e,t,n,r){let o=A(),i=Ms(2);o.firstUpdatePass&&ep(o,null,i,r);let s=I();if(n!==le&&ce(s,i,n)){let a=o.data[Le()];if(np(a,r)&&!Xf(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Xr(c,n||"")),Na(o,a,s,n,r)}else qI(o,a,s,s[O],s[i+1],s[i+1]=$I(e,t,n),r,i)}}function Xf(e,t){return t>=e.expandoStartIndex}function ep(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Le()],s=Xf(e,n);np(i,r)&&t===null&&!s&&(t=!1),t=jI(o,i,t,r),NI(o,i,t,n,s,r)}}function jI(e,t,n,r){let o=Zl(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Ws(null,e,t,n,r),n=Qn(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Ws(o,e,t,n,r),i===null){let c=VI(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Ws(null,e,t,c[1],r),c=Qn(c,t.attrs,r),HI(e,t,r,c))}else i=BI(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function VI(e,t,n){let r=n?t.classBindings:t.styleBindings;if(nn(r)!==0)return e[Ot(r)]}function HI(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Ot(o)]=r}function BI(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Qn(r,s,n)}return Qn(r,t.attrs,n)}function Ws(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Qn(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Qn(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),oo(e,s,n?!0:t[++i]))}return e===void 0?null:e}function $I(e,t,n){if(n==null||n==="")return z;let r=[],o=Yn(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function UI(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&oo(e,r,n)}function qI(e,t,n,r,o,i,s,a){o===le&&(o=z);let c=0,l=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;u!==null||d!==null;){let p=c<o.length?o[c+1]:void 0,f=l<i.length?i[l+1]:void 0,h=null,g;u===d?(c+=2,l+=2,p!==f&&(h=d,g=f)):d===null||u!==null&&u<d?(c+=2,h=u):(l+=2,h=d,g=f),h!==null&&tp(e,t,n,r,h,g,s,a),u=c<o.length?o[c]:null,d=l<i.length?i[l]:null}}function tp(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=MI(l)?Fu(c,t,n,o,nn(l),s):void 0;if(!Qo(u)){Qo(i)||TI(l)&&(i=Fu(c,null,n,o,a,s));let d=gs(Le(),n);hy(r,s,d,o,i)}}function Fu(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,p=n[o+1];p===le&&(p=d?z:void 0);let f=d?io(p,r):u===r?p:void 0;if(l&&!Qo(f)&&(f=io(c,r)),Qo(f)&&(a=f,s))return a;let h=e[o+1];o=s?Ot(h):nn(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=io(c,r))}return a}function Qo(e){return e!==void 0}function WI(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=ee(Yn(e)))),e}function np(e,t){return(e.flags&(t?8:16))!==0}function GI(e,t=""){let n=I(),r=A(),o=e+L,i=r.firstCreatePass?er(r,o,1,t,null):r.data[o],s=zI(r,n,i,t,e);n[o]=s,kn()&&ni(r,n,s,i),Je(i,!1)}var zI=(e,t,n,r,o)=>(Pn(!0),Om(t[O],r));function rp(e,t,n,r=""){return ce(e,Ke(),n)?t+bn(n)+r:le}function op(e){return mc("",e),op}function mc(e,t,n){let r=I(),o=rp(r,e,t,n);return o!==le&&QI(r,Le(),o),mc}function QI(e,t,n){let r=gs(t,e);Am(e[O],r,n)}function ip(e,t,n){As(t)&&(t=t());let r=I(),o=Ke();if(ce(r,o,t)){let i=A(),s=An();Fd(s,r,e,t,r[O],n)}return ip}function ZI(e,t){let n=As(e);return n&&e.set(t),n}function sp(e,t){let n=I(),r=A(),o=U();return zf(r,n,n[O],o,e,t),sp}function YI(e,t,n=""){return rp(I(),e,t,n)}function JI(e,t,n){let r=A();if(r.firstCreatePass){let o=Me(e);Ra(n,r.data,r.blueprint,o,!0),Ra(t,r.data,r.blueprint,o,!1)}}function Ra(e,t,n,r,o){if(e=G(e),Array.isArray(e))for(let i=0;i<e.length;i++)Ra(e[i],t,n,r,o);else{let i=A(),s=I(),a=U(),c=yt(e)?e:G(e.provide),l=cs(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(yt(e)||!e.multi){let f=new Rt(l,o,nr),h=zs(c,t,o?u:u+p,d);h===-1?(Zs(Po(a,s),i,c),Gs(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=zs(c,t,u+p,d),h=zs(c,t,u,u+p),g=f>=0&&n[f],N=h>=0&&n[h];if(o&&!N||!o&&!g){Zs(Po(a,s),i,c);let b=eE(o?XI:KI,n.length,o,r,l);!o&&N&&(n[h].providerFactory=b),Gs(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(b),s.push(b)}else{let b=ap(n[o?h:f],l,!o&&r);Gs(i,e,f>-1?f:h,b)}!o&&r&&N&&n[h].componentProviders++}}}function Gs(e,t,n,r){let o=yt(t),i=Nl(t);if(o||i){let c=(i?G(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function ap(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function zs(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function KI(e,t,n,r){return Oa(this.multi,[])}function XI(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Hn(n,n[D],this.providerFactory.index,r);i=a.slice(0,s),Oa(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Oa(o,i);return i}function Oa(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function eE(e,t,n,r,o){let i=new Rt(e,n,nr);return i.multi=[],i.index=t,i.componentProviders=0,ap(i,o,r&&!n),i}function tE(e,t=[]){return n=>{n.providersResolver=(r,o)=>JI(r,o?o(e):e,t)}}function cp(e,t){let n=e[t];return n===le?void 0:n}function nE(e,t,n,r,o,i){let s=t+n;return ce(e,s,o)?ff(e,s+1,i?r.call(i,o):r(o)):cp(e,s+1)}function rE(e,t,n,r,o,i,s){let a=t+n;return Qy(e,a,o,i)?ff(e,a+2,s?r.call(s,o,i):r(o,i)):cp(e,a+2)}function oE(e,t){let n=A(),r,o=e+L;n.firstCreatePass?(r=iE(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Ue(r.type,!0)),s,a=Z(nr);try{let c=ko(!1),l=i();return ko(c),ms(n,I(),o,l),l}finally{Z(a)}}function iE(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function sE(e,t,n){let r=e+L,o=I(),i=lo(o,r);return lp(o,r)?nE(o,_s(),t,i.transform,n,i):i.transform(n)}function aE(e,t,n,r){let o=e+L,i=I(),s=lo(i,o);return lp(i,o)?rE(i,_s(),t,s.transform,n,r,s):s.transform(n,r)}function lp(e,t){return e[D].data[t].pure}function cE(e,t){return oi(e,t)}var Zo=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},lE=(()=>{class e{compileModuleSync(n){return new qo(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=rs(n),i=_d(o.declarations).reduce((s,a)=>{let c=Ce(a);return c&&s.push(new tt(c)),s},[]);return new Zo(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var uE=(()=>{class e{zone=m(K);changeDetectionScheduler=m(te);applicationRef=m(rr);applicationErrorHandler=m(Ne);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function up({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new K(W(q({},dp()),{scheduleInRootZone:n})),[{provide:K,useFactory:e},{provide:Ge,multi:!0,useFactory:()=>{let r=m(uE,{optional:!0});return()=>r.initialize()}},{provide:Ge,multi:!0,useFactory:()=>{let r=m(dE);return()=>{r.initialize()}}},t===!0?{provide:ks,useValue:!0}:[],{provide:Do,useValue:n??Sf},{provide:Ne,useFactory:()=>{let r=m(K),o=m(oe),i;return s=>{i??=o.get(ke),r.runOutsideAngular(()=>i.handleError(s))}}}]}function dp(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var dE=(()=>{class e{subscription=new F;initialized=!1;zone=m(K);pendingTasks=m(Mt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{K.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{K.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var yc=(()=>{class e{applicationErrorHandler=m(Ne);appRef=m(rr);taskService=m(Mt);ngZone=m(K);zonelessEnabled=m(Ln);tracing=m(an,{optional:!0});disableScheduling=m(ks,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new F;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Wo):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(m(Do,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Gn||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Su:Rf;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Wo+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Su(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fE(){return nt("NgZoneless"),$t([{provide:te,useExisting:yc},{provide:K,useClass:Gn},{provide:Ln,useValue:!0},{provide:Do,useValue:!1},[]])}function pE(){return typeof $localize<"u"&&$localize.locale||or}var vc=new x("",{providedIn:"root",factory:()=>m(vc,{optional:!0,skipSelf:!0})||pE()});function hE(e){return ul(e)}function gE(e,t){return un(e,t?.equal)}var Ic=class{[V];constructor(t){this[V]=t}destroy(){this[V].destroy()}};function mE(e,t){let n=t?.injector??m(de),r=t?.manualCleanup!==!0?n.get(Fe):null,o,i=n.get(_t,null,{optional:!0}),s=n.get(te);return i!==null?(o=IE(i.view,s,e),r instanceof wn&&r._lView===i.view&&(r=null)):o=EE(e,n.get(Fn),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Ic(o)}var fp=W(q({},Be),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Nt,run(){if(this.dirty=!1,this.hasRun&&!ot(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Re(this),n=Qt(!1);try{this.maybeCleanup(),this.fn(e)}finally{Qt(n),$e(this,t)}},maybeCleanup(){if(!this.cleanupFns?.length)return;let e=w(null);try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[],w(e)}}}),yE=W(q({},fp),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){kt(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),vE=W(q({},fp),{consumerMarkedDirty(){this.view[y]|=8192,Ye(this.view),this.notifier.notify(13)},destroy(){kt(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Pe]?.delete(this)}});function IE(e,t,n){let r=Object.create(vE);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Pe]??=new Set,e[Pe].add(r),r.consumerMarkedDirty(r),r}function EE(e,t,n){let r=Object.create(yE);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.add(r),r.notifier.notify(12),r}var Ip=Symbol("InputSignalNode#UNSET"),wE=W(q({},dn),{transformFn:void 0,applyValueToInputSignal(e,t){it(e,t)}});function Ep(e,t){let n=Object.create(wE);n.value=e,n.transformFn=t?.transform;function r(){if(rt(n),n.value===Ip){let o=null;throw new _(-950,o)}return n.value}return r[V]=n,r}var pp=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>nd(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},CE=new x("");CE.__NG_ELEMENT_ID__=e=>{let t=U();if(t===null)throw new _(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new _(204,!1)};function hp(e,t){return Ep(e,t)}function bE(e){return Ep(Ip,e)}var nP=(hp.required=bE,hp);function gp(e,t){return nc(t)}function TE(e,t){return rc(t)}var rP=(gp.required=TE,gp);function mp(e,t){return nc(t)}function _E(e,t){return rc(t)}var oP=(mp.required=_E,mp);var Dc=new x(""),ME=new x("");function ir(e){return!e.moduleRef}function NE(e){let t=ir(e)?e.r3Injector:e.moduleRef.injector,n=t.get(K);return n.run(()=>{ir(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Ne),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),ir(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Dc);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Dc);s.add(i),e.moduleRef.onDestroy(()=>{Vn(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return SE(r,n,()=>{let i=t.get(uc);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(vc,or);if(Wf(s||or),!t.get(ME,!0))return ir(e)?t.get(rr):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(ir(e)){let c=t.get(rr);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return xE?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var xE;function SE(e,t,n){try{let r=n();return cc(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var li=null;function RE(e=[],t){return de.create({name:t,providers:[{provide:as,useValue:"platform"},{provide:Dc,useValue:new Set([()=>li=null])},...e]})}function OE(e=[]){if(li)return li;let t=RE(e);return li=t,Ff(),AE(t),t}function AE(e){let t=e.get(ud,null);so(e,()=>{t?.forEach(n=>n())})}var iP=(()=>{class e{static __NG_ELEMENT_ID__=kE}return e})();function kE(e){return PE(U(),I(),(e&16)===16)}function PE(e,t,n){if(Ze(e)&&!n){let r=ae(e.index,t);return new et(r,r)}else if(e.type&175){let r=t[J];return new et(r,t)}return null}var wc=class{constructor(){}supports(t){return Xa(t)}create(t){return new Cc(t)}},LE=(e,t)=>t,Cc=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||LE}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<yp(r,o,i)?n:r,a=yp(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let p=0;p<l;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;u<=h&&h<l&&(i[p]=f+1)}let d=s.previousIndex;i[d]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Xa(t))throw new _(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,uf(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new bc(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new ui),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new ui),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},bc=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Tc=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},ui=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Tc,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function yp(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function vp(){return new FE([new wc])}var FE=(()=>{class e{factories;static \u0275prov=B({token:e,providedIn:"root",factory:vp});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||vp()),deps:[[e,new Hu,new Vu]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new _(901,!1)}}return e})();function sP(e){S(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=OE(r),i=[up({}),{provide:te,useExisting:yc},iu,...n||[]],s=new Wn({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return NE({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{S(9)}}function aP(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function cP(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}var Ec=Symbol("NOT_SET"),Dp=new Set,jE=W(q({},dn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,value:Ec,cleanup:null,consumerMarkedDirty(){if(this.sequence.impl.executing){if(this.sequence.lastPhase===null||this.sequence.lastPhase<this.phase)return;this.sequence.erroredOrDestroyed=!0}this.sequence.scheduler.notify(7)},phaseFn(e){if(this.sequence.lastPhase=this.phase,!this.dirty)return this.signal;if(this.dirty=!1,this.value!==Ec&&!ot(this))return this.signal;try{for(let o of this.cleanup??Dp)o()}finally{this.cleanup?.clear()}let t=[];e!==void 0&&t.push(e),t.push(this.registerCleanupFn);let n=Re(this),r;try{r=this.userFn.apply(null,t)}finally{$e(this,n)}return(this.value===Ec||!this.equal(this.value,r))&&(this.value=r,this.version++),this.signal}}),_c=class extends zn{scheduler;lastPhase=null;nodes=[void 0,void 0,void 0,void 0];constructor(t,n,r,o,i,s=null){super(t,[void 0,void 0,void 0,void 0],r,!1,i,s),this.scheduler=o;for(let a of sc){let c=n[a];if(c===void 0)continue;let l=Object.create(jE);l.sequence=this,l.phase=a,l.userFn=c,l.dirty=!0,l.signal=()=>(rt(l),l.value),l.signal[V]=l,l.registerCleanupFn=u=>(l.cleanup??=new Set).add(u),this.nodes[a]=l,this.hooks[a]=u=>l.phaseFn(u)}}afterRun(){super.afterRun(),this.lastPhase=null}destroy(){super.destroy();for(let t of this.nodes)for(let n of t?.cleanup??Dp)n()}};function lP(e,t){let n=t?.injector??m(de),r=n.get(te),o=n.get(ci),i=n.get(an,null,{optional:!0});o.impl??=n.get(ac);let s=e;typeof s=="function"&&(s={mixedReadWrite:e});let a=n.get(_t,null,{optional:!0}),c=new _c(o.impl,[s.earlyRead,s.write,s.mixedReadWrite,s.read],a?.view,r,n.get(Fe),i?.snapshot(null));return o.impl.register(c),c}function uP(e,t){let n=Ce(e),r=t.elementInjector||Ut();return new tt(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector,t.directives,t.bindings)}function dP(e){let t=Ce(e);if(!t)return null;let n=new tt(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}export{q as a,W as b,VE as c,F as d,Pp as e,M as f,_i as g,Mi as h,re as i,pn as j,lt as k,Ee as l,Wp as m,Gp as n,zp as o,dt as p,ft as q,th as r,pt as s,yn as t,Wr as u,rh as v,oh as w,xi as x,ih as y,ht as z,sh as A,cl as B,ah as C,ch as D,vn as E,Si as F,lh as G,fh as H,ph as I,Ri as J,hh as K,gh as L,mh as M,Ai as N,yh as O,vh as P,Ih as Q,Eh as R,Dh as S,wh as T,Ch as U,_ as V,eo as W,B as X,ml as Y,_h as Z,x as _,De as $,m as aa,$t as ba,as as ca,oe as da,so as ea,Bl as fa,$l as ga,eu as ha,tu as ia,de as ja,ou as ka,Fe as la,ke as ma,Ne as na,Kh as oa,Rs as pa,Eo as qa,te as ra,Mt as sa,Ps as ta,cg as ua,_g as va,nd as wa,Zn as xa,Lo as ya,Ag as za,kg as Aa,ud as Ba,Lg as Ca,Fg as Da,jg as Ea,Kt as Fa,Yn as Ga,Fa as Ha,Gg as Ia,zg as Ja,Qg as Ka,Zg as La,Yg as Ma,ja as Na,fm as Oa,Jo as Pa,gm as Qa,Hm as Ra,jo as Sa,Un as Ta,qn as Ua,Ly as Va,nr as Wa,jy as Xa,si as Ya,nt as Za,en as _a,bf as $a,Tf as ab,Ev as bb,Cv as cb,_v as db,Mv as eb,Nf as fb,xf as gb,an as hb,je as ib,K as jb,Af as kb,Gv as lb,cc as mb,zv as nb,Lf as ob,rr as pb,Vf as qb,Yv as rb,Jv as sb,Kv as tb,Xv as ub,eI as vb,Hf as wb,fc as xb,pc as yb,Bf as zb,hc as Ab,gc as Bb,$f as Cb,sI as Db,Uf as Eb,lI as Fb,qf as Gb,Gf as Hb,fI as Ib,hI as Jb,gI as Kb,yI as Lb,vI as Mb,II as Nb,EI as Ob,DI as Pb,wI as Qb,CI as Rb,bI as Sb,Yf as Tb,Jf as Ub,PI as Vb,GI as Wb,op as Xb,mc as Yb,ip as Zb,ZI as _b,sp as $b,YI as ac,tE as bc,oE as cc,sE as dc,aE as ec,cE as fc,lE as gc,fE as hc,vc as ic,hE as jc,gE as kc,mE as lc,pp as mc,CE as nc,nP as oc,rP as pc,oP as qc,iP as rc,FE as sc,sP as tc,aP as uc,cP as vc,lP as wc,uP as xc,dP as yc};
