import{V as E,X as Tt,a as me,c as Et}from"./chunk-AWEQXHCH.js";var _=function(n){return n[n.State=0]="State",n[n.Transition=1]="Transition",n[n.Sequence=2]="Sequence",n[n.Group=3]="Group",n[n.Animate=4]="Animate",n[n.Keyframes=5]="Keyframes",n[n.Style=6]="Style",n[n.Trigger=7]="Trigger",n[n.Reference=8]="Reference",n[n.AnimateChild=9]="AnimateChild",n[n.AnimateRef=10]="AnimateRef",n[n.Query=11]="Query",n[n.Stagger=12]="Stagger",n}(_||{}),z="*";function vt(n,e=null){return{type:_.Sequence,steps:n,options:e}}function Le(n){return{type:_.Style,styles:n,offset:null}}var $=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(e=0,t=0){this.totalTime=e+t}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(e=>e()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(e){this._position=this.totalTime?e*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(e){let t=e=="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},ee=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(e){this.players=e;let t=0,s=0,i=0,r=this.players.length;r==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(a=>{a.onDone(()=>{++t==r&&this._onFinish()}),a.onDestroy(()=>{++s==r&&this._onDestroy()}),a.onStart(()=>{++i==r&&this._onStart()})}),this.totalTime=this.players.reduce((a,o)=>Math.max(a,o.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this.players.forEach(e=>e.init())}onStart(e){this._onStartFns.push(e)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(e=>e()),this._onStartFns=[])}onDone(e){this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(e=>e.play())}pause(){this.players.forEach(e=>e.pause())}restart(){this.players.forEach(e=>e.restart())}finish(){this._onFinish(),this.players.forEach(e=>e.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(e=>e.destroy()),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this.players.forEach(e=>e.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(e){let t=e*this.totalTime;this.players.forEach(s=>{let i=s.totalTime?Math.min(1,t/s.totalTime):1;s.setPosition(i)})}getPosition(){let e=this.players.reduce((t,s)=>t===null||s.totalTime>t.totalTime?s:t,null);return e!=null?e.getPosition():0}beforeDestroy(){this.players.forEach(e=>{e.beforeDestroy&&e.beforeDestroy()})}triggerCallback(e){let t=e=="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},re="!";function bt(n){return new E(3e3,!1)}function bs(){return new E(3100,!1)}function ws(){return new E(3101,!1)}function Ps(n){return new E(3001,!1)}function As(n){return new E(3003,!1)}function Ns(n){return new E(3004,!1)}function Pt(n,e){return new E(3005,!1)}function At(){return new E(3006,!1)}function Nt(){return new E(3007,!1)}function Ct(n,e){return new E(3008,!1)}function Dt(n){return new E(3002,!1)}function Mt(n,e,t,s,i){return new E(3010,!1)}function kt(){return new E(3011,!1)}function Ft(){return new E(3012,!1)}function Rt(){return new E(3200,!1)}function Ot(){return new E(3202,!1)}function Lt(){return new E(3013,!1)}function It(n){return new E(3014,!1)}function zt(n){return new E(3015,!1)}function Kt(n){return new E(3016,!1)}function qt(n){return new E(3500,!1)}function Bt(n){return new E(3501,!1)}function Qt(n,e){return new E(3404,!1)}function Cs(n){return new E(3502,!1)}function Vt(n){return new E(3503,!1)}function $t(){return new E(3300,!1)}function Ut(n){return new E(3504,!1)}function Gt(n){return new E(3301,!1)}function jt(n,e){return new E(3302,!1)}function Wt(n){return new E(3303,!1)}function Ht(n,e){return new E(3400,!1)}function Yt(n){return new E(3401,!1)}function Xt(n){return new E(3402,!1)}function xt(n,e){return new E(3505,!1)}var Ds=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function U(n){switch(n.length){case 0:return new $;case 1:return n[0];default:return new ee(n)}}function qe(n,e,t=new Map,s=new Map){let i=[],r=[],a=-1,o=null;if(e.forEach(l=>{let u=l.get("offset"),c=u==a,h=c&&o||new Map;l.forEach((S,y)=>{let d=y,g=S;if(y!=="offset")switch(d=n.normalizePropertyName(d,i),g){case re:g=t.get(y);break;case z:g=s.get(y);break;default:g=n.normalizeStyleValue(y,d,g,i);break}h.set(d,g)}),c||r.push(h),o=h,a=u}),i.length)throw Cs(i);return r}function pe(n,e,t,s){switch(e){case"start":n.onStart(()=>s(t&&Ie(t,"start",n)));break;case"done":n.onDone(()=>s(t&&Ie(t,"done",n)));break;case"destroy":n.onDestroy(()=>s(t&&Ie(t,"destroy",n)));break}}function Ie(n,e,t){let s=t.totalTime,i=!!t.disabled,r=ge(n.element,n.triggerName,n.fromState,n.toState,e||n.phaseName,s??n.totalTime,i),a=n._data;return a!=null&&(r._data=a),r}function ge(n,e,t,s,i="",r=0,a){return{element:n,triggerName:e,fromState:t,toState:s,phaseName:i,totalTime:r,disabled:!!a}}function F(n,e,t){let s=n.get(e);return s||n.set(e,s=t),s}function Be(n){let e=n.indexOf(":"),t=n.substring(1,e),s=n.slice(e+1);return[t,s]}var Ms=typeof document>"u"?null:document.documentElement;function ye(n){let e=n.parentNode||n.host||null;return e===Ms?null:e}function ks(n){return n.substring(1,6)=="ebkit"}var X=null,wt=!1;function Jt(n){X||(X=Rs()||{},wt=X.style?"WebkitAppearance"in X.style:!1);let e=!0;return X.style&&!ks(n)&&(e=n in X.style,!e&&wt&&(e="Webkit"+n.charAt(0).toUpperCase()+n.slice(1)in X.style)),e}function Fs(n){return Ds.has(n)}function Rs(){return typeof document<"u"?document.body:null}function Qe(n,e){for(;e;){if(e===n)return!0;e=ye(e)}return!1}function Ve(n,e,t){if(t)return Array.from(n.querySelectorAll(e));let s=n.querySelector(e);return s?[s]:[]}var Os=1e3,$e="{{",Ls="}}",_e="ng-enter",ae="ng-leave",oe="ng-trigger",le=".ng-trigger",Ue="ng-animating",Se=".ng-animating";function V(n){if(typeof n=="number")return n;let e=n.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:ze(parseFloat(e[1]),e[2])}function ze(n,e){switch(e){case"s":return n*Os;default:return n}}function ue(n,e,t){return n.hasOwnProperty("duration")?n:Is(n,e,t)}function Is(n,e,t){let s=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,r=0,a="";if(typeof n=="string"){let o=n.match(s);if(o===null)return e.push(bt(n)),{duration:0,delay:0,easing:""};i=ze(parseFloat(o[1]),o[2]);let l=o[3];l!=null&&(r=ze(parseFloat(l),o[4]));let u=o[5];u&&(a=u)}else i=n;if(!t){let o=!1,l=e.length;i<0&&(e.push(bs()),o=!0),r<0&&(e.push(ws()),o=!0),o&&e.splice(l,0,bt(n))}return{duration:i,delay:r,easing:a}}function Zt(n){return n.length?n[0]instanceof Map?n:n.map(e=>new Map(Object.entries(e))):[]}function Ge(n){return Array.isArray(n)?new Map(...n):new Map(n)}function K(n,e,t){e.forEach((s,i)=>{let r=Ee(i);t&&!t.has(i)&&t.set(i,n.style[r]),n.style[r]=s})}function j(n,e){e.forEach((t,s)=>{let i=Ee(s);n.style[i]=""})}function te(n){return Array.isArray(n)?n.length==1?n[0]:vt(n):n}function es(n,e,t){let s=e.params||{},i=je(n);i.length&&i.forEach(r=>{s.hasOwnProperty(r)||t.push(Ps(r))})}var Ke=new RegExp(`${$e}\\s*(.+?)\\s*${Ls}`,"g");function je(n){let e=[];if(typeof n=="string"){let t;for(;t=Ke.exec(n);)e.push(t[1]);Ke.lastIndex=0}return e}function se(n,e,t){let s=`${n}`,i=s.replace(Ke,(r,a)=>{let o=e[a];return o==null&&(t.push(As(a)),o=""),o.toString()});return i==s?n:i}var zs=/-+([a-z0-9])/g;function Ee(n){return n.replace(zs,(...e)=>e[1].toUpperCase())}function Ks(n){return n.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function ts(n,e){return n===0||e===0}function ss(n,e,t){if(t.size&&e.length){let s=e[0],i=[];if(t.forEach((r,a)=>{s.has(a)||i.push(a),s.set(a,r)}),i.length)for(let r=1;r<e.length;r++){let a=e[r];i.forEach(o=>a.set(o,Te(n,o)))}}return e}function R(n,e,t){switch(e.type){case _.Trigger:return n.visitTrigger(e,t);case _.State:return n.visitState(e,t);case _.Transition:return n.visitTransition(e,t);case _.Sequence:return n.visitSequence(e,t);case _.Group:return n.visitGroup(e,t);case _.Animate:return n.visitAnimate(e,t);case _.Keyframes:return n.visitKeyframes(e,t);case _.Style:return n.visitStyle(e,t);case _.Reference:return n.visitReference(e,t);case _.AnimateChild:return n.visitAnimateChild(e,t);case _.AnimateRef:return n.visitAnimateRef(e,t);case _.Query:return n.visitQuery(e,t);case _.Stagger:return n.visitStagger(e,t);default:throw Ns(e.type)}}function Te(n,e){return window.getComputedStyle(n)[e]}var gs=(()=>{class n{validateStyleProperty(t){return Jt(t)}containsElement(t,s){return Qe(t,s)}getParentElement(t){return ye(t)}query(t,s,i){return Ve(t,s,i)}computeStyle(t,s,i){return i||""}animate(t,s,i,r,a,o=[],l){return new $(i,r)}static \u0275fac=function(s){return new(s||n)};static \u0275prov=Tt({token:n,factory:n.\u0275fac})}return n})(),is=class{static NOOP=new gs},Je=class{},Ze=class{normalizePropertyName(e,t){return e}normalizeStyleValue(e,t,s,i){return s}},qs=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),et=class extends Je{normalizePropertyName(e,t){return Ee(e)}normalizeStyleValue(e,t,s,i){let r="",a=s.toString().trim();if(qs.has(t)&&s!==0&&s!=="0")if(typeof s=="number")r="px";else{let o=s.match(/^[+-]?[\d\.]+([a-z]*)$/);o&&o[1].length==0&&i.push(Pt(e,s))}return a+r}};var Ae="*";function Bs(n,e){let t=[];return typeof n=="string"?n.split(/\s*,\s*/).forEach(s=>Qs(s,t,e)):t.push(n),t}function Qs(n,e,t){if(n[0]==":"){let l=Vs(n,t);if(typeof l=="function"){e.push(l);return}n=l}let s=n.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(s==null||s.length<4)return t.push(zt(n)),e;let i=s[1],r=s[2],a=s[3];e.push(ns(i,a));let o=i==Ae&&a==Ae;r[0]=="<"&&!o&&e.push(ns(a,i))}function Vs(n,e){switch(n){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,s)=>parseFloat(s)>parseFloat(t);case":decrement":return(t,s)=>parseFloat(s)<parseFloat(t);default:return e.push(Kt(n)),"* => *"}}var ve=new Set(["true","1"]),be=new Set(["false","0"]);function ns(n,e){let t=ve.has(n)||be.has(n),s=ve.has(e)||be.has(e);return(i,r)=>{let a=n==Ae||n==i,o=e==Ae||e==r;return!a&&t&&typeof i=="boolean"&&(a=i?ve.has(n):be.has(n)),!o&&s&&typeof r=="boolean"&&(o=r?ve.has(e):be.has(e)),a&&o}}var ys=":self",$s=new RegExp(`s*${ys}s*,?`,"g");function dt(n,e,t,s){return new tt(n).build(e,t,s)}var rs="",tt=class{_driver;constructor(e){this._driver=e}build(e,t,s){let i=new st(t);return this._resetContextStyleTimingState(i),R(this,te(e),i)}_resetContextStyleTimingState(e){e.currentQuerySelector=rs,e.collectedStyles=new Map,e.collectedStyles.set(rs,new Map),e.currentTime=0}visitTrigger(e,t){let s=t.queryCount=0,i=t.depCount=0,r=[],a=[];return e.name.charAt(0)=="@"&&t.errors.push(At()),e.definitions.forEach(o=>{if(this._resetContextStyleTimingState(t),o.type==_.State){let l=o,u=l.name;u.toString().split(/\s*,\s*/).forEach(c=>{l.name=c,r.push(this.visitState(l,t))}),l.name=u}else if(o.type==_.Transition){let l=this.visitTransition(o,t);s+=l.queryCount,i+=l.depCount,a.push(l)}else t.errors.push(Nt())}),{type:_.Trigger,name:e.name,states:r,transitions:a,queryCount:s,depCount:i,options:null}}visitState(e,t){let s=this.visitStyle(e.styles,t),i=e.options&&e.options.params||null;if(s.containsDynamicStyles){let r=new Set,a=i||{};s.styles.forEach(o=>{o instanceof Map&&o.forEach(l=>{je(l).forEach(u=>{a.hasOwnProperty(u)||r.add(u)})})}),r.size&&t.errors.push(Ct(e.name,[...r.values()]))}return{type:_.State,name:e.name,style:s,options:i?{params:i}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;let s=R(this,te(e.animation),t),i=Bs(e.expr,t.errors);return{type:_.Transition,matchers:i,animation:s,queryCount:t.queryCount,depCount:t.depCount,options:x(e.options)}}visitSequence(e,t){return{type:_.Sequence,steps:e.steps.map(s=>R(this,s,t)),options:x(e.options)}}visitGroup(e,t){let s=t.currentTime,i=0,r=e.steps.map(a=>{t.currentTime=s;let o=R(this,a,t);return i=Math.max(i,t.currentTime),o});return t.currentTime=i,{type:_.Group,steps:r,options:x(e.options)}}visitAnimate(e,t){let s=Ws(e.timings,t.errors);t.currentAnimateTimings=s;let i,r=e.styles?e.styles:Le({});if(r.type==_.Keyframes)i=this.visitKeyframes(r,t);else{let a=e.styles,o=!1;if(!a){o=!0;let u={};s.easing&&(u.easing=s.easing),a=Le(u)}t.currentTime+=s.duration+s.delay;let l=this.visitStyle(a,t);l.isEmptyStep=o,i=l}return t.currentAnimateTimings=null,{type:_.Animate,timings:s,style:i,options:null}}visitStyle(e,t){let s=this._makeStyleAst(e,t);return this._validateStyleAst(s,t),s}_makeStyleAst(e,t){let s=[],i=Array.isArray(e.styles)?e.styles:[e.styles];for(let o of i)typeof o=="string"?o===z?s.push(o):t.errors.push(Dt(o)):s.push(new Map(Object.entries(o)));let r=!1,a=null;return s.forEach(o=>{if(o instanceof Map&&(o.has("easing")&&(a=o.get("easing"),o.delete("easing")),!r)){for(let l of o.values())if(l.toString().indexOf($e)>=0){r=!0;break}}}),{type:_.Style,styles:s,easing:a,offset:e.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(e,t){let s=t.currentAnimateTimings,i=t.currentTime,r=t.currentTime;s&&r>0&&(r-=s.duration+s.delay),e.styles.forEach(a=>{typeof a!="string"&&a.forEach((o,l)=>{let u=t.collectedStyles.get(t.currentQuerySelector),c=u.get(l),h=!0;c&&(r!=i&&r>=c.startTime&&i<=c.endTime&&(t.errors.push(Mt(l,c.startTime,c.endTime,r,i)),h=!1),r=c.startTime),h&&u.set(l,{startTime:r,endTime:i}),t.options&&es(o,t.options,t.errors)})})}visitKeyframes(e,t){let s={type:_.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(kt()),s;let i=1,r=0,a=[],o=!1,l=!1,u=0,c=e.steps.map(w=>{let P=this._makeStyleAst(w,t),D=P.offset!=null?P.offset:js(P.styles),N=0;return D!=null&&(r++,N=P.offset=D),l=l||N<0||N>1,o=o||N<u,u=N,a.push(N),P});l&&t.errors.push(Ft()),o&&t.errors.push(Rt());let h=e.steps.length,S=0;r>0&&r<h?t.errors.push(Ot()):r==0&&(S=i/(h-1));let y=h-1,d=t.currentTime,g=t.currentAnimateTimings,v=g.duration;return c.forEach((w,P)=>{let D=S>0?P==y?1:S*P:a[P],N=D*v;t.currentTime=d+g.delay+N,g.duration=N,this._validateStyleAst(w,t),w.offset=D,s.styles.push(w)}),s}visitReference(e,t){return{type:_.Reference,animation:R(this,te(e.animation),t),options:x(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:_.AnimateChild,options:x(e.options)}}visitAnimateRef(e,t){return{type:_.AnimateRef,animation:this.visitReference(e.animation,t),options:x(e.options)}}visitQuery(e,t){let s=t.currentQuerySelector,i=e.options||{};t.queryCount++,t.currentQuery=e;let[r,a]=Us(e.selector);t.currentQuerySelector=s.length?s+" "+r:r,F(t.collectedStyles,t.currentQuerySelector,new Map);let o=R(this,te(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=s,{type:_.Query,selector:r,limit:i.limit||0,optional:!!i.optional,includeSelf:a,animation:o,originalSelector:e.selector,options:x(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(Lt());let s=e.timings==="full"?{duration:0,delay:0,easing:"full"}:ue(e.timings,t.errors,!0);return{type:_.Stagger,animation:R(this,te(e.animation),t),timings:s,options:null}}};function Us(n){let e=!!n.split(/\s*,\s*/).find(t=>t==ys);return e&&(n=n.replace($s,"")),n=n.replace(/@\*/g,le).replace(/@\w+/g,t=>le+"-"+t.slice(1)).replace(/:animating/g,Se),[n,e]}function Gs(n){return n?me({},n):null}var st=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(e){this.errors=e}};function js(n){if(typeof n=="string")return null;let e=null;if(Array.isArray(n))n.forEach(t=>{if(t instanceof Map&&t.has("offset")){let s=t;e=parseFloat(s.get("offset")),s.delete("offset")}});else if(n instanceof Map&&n.has("offset")){let t=n;e=parseFloat(t.get("offset")),t.delete("offset")}return e}function Ws(n,e){if(n.hasOwnProperty("duration"))return n;if(typeof n=="number"){let r=ue(n,e).duration;return We(r,0,"")}let t=n;if(t.split(/\s+/).some(r=>r.charAt(0)=="{"&&r.charAt(1)=="{")){let r=We(0,0,"");return r.dynamic=!0,r.strValue=t,r}let i=ue(t,e);return We(i.duration,i.delay,i.easing)}function x(n){return n?(n=me({},n),n.params&&(n.params=Gs(n.params))):n={},n}function We(n,e,t){return{duration:n,delay:e,easing:t}}function mt(n,e,t,s,i,r,a=null,o=!1){return{type:1,element:n,keyframes:e,preStyleProps:t,postStyleProps:s,duration:i,delay:r,totalTime:i+r,easing:a,subTimeline:o}}var ne=class{_map=new Map;get(e){return this._map.get(e)||[]}append(e,t){let s=this._map.get(e);s||this._map.set(e,s=[]),s.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}},Hs=1,Ys=":enter",Xs=new RegExp(Ys,"g"),xs=":leave",Js=new RegExp(xs,"g");function pt(n,e,t,s,i,r=new Map,a=new Map,o,l,u=[]){return new it().buildKeyframes(n,e,t,s,i,r,a,o,l,u)}var it=class{buildKeyframes(e,t,s,i,r,a,o,l,u,c=[]){u=u||new ne;let h=new nt(e,t,u,i,r,c,[]);h.options=l;let S=l.delay?V(l.delay):0;h.currentTimeline.delayNextStep(S),h.currentTimeline.setStyles([a],null,h.errors,l),R(this,s,h);let y=h.timelines.filter(d=>d.containsAnimation());if(y.length&&o.size){let d;for(let g=y.length-1;g>=0;g--){let v=y[g];if(v.element===t){d=v;break}}d&&!d.allowOnlyTimelineStyles()&&d.setStyles([o],null,h.errors,l)}return y.length?y.map(d=>d.buildKeyframes()):[mt(t,[],[],[],0,S,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){let s=t.subInstructions.get(t.element);if(s){let i=t.createSubContext(e.options),r=t.currentTimeline.currentTime,a=this._visitSubInstructions(s,i,i.options);r!=a&&t.transformIntoNewTimeline(a)}t.previousNode=e}visitAnimateRef(e,t){let s=t.createSubContext(e.options);s.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,s),this.visitReference(e.animation,s),t.transformIntoNewTimeline(s.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,s){for(let i of e){let r=i?.delay;if(r){let a=typeof r=="number"?r:V(se(r,i?.params??{},t.errors));s.delayNextStep(a)}}}_visitSubInstructions(e,t,s){let r=t.currentTimeline.currentTime,a=s.duration!=null?V(s.duration):null,o=s.delay!=null?V(s.delay):null;return a!==0&&e.forEach(l=>{let u=t.appendInstructionToTimeline(l,a,o);r=Math.max(r,u.duration+u.delay)}),r}visitReference(e,t){t.updateOptions(e.options,!0),R(this,e.animation,t),t.previousNode=e}visitSequence(e,t){let s=t.subContextCount,i=t,r=e.options;if(r&&(r.params||r.delay)&&(i=t.createSubContext(r),i.transformIntoNewTimeline(),r.delay!=null)){i.previousNode.type==_.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=Ne);let a=V(r.delay);i.delayNextStep(a)}e.steps.length&&(e.steps.forEach(a=>R(this,a,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>s&&i.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){let s=[],i=t.currentTimeline.currentTime,r=e.options&&e.options.delay?V(e.options.delay):0;e.steps.forEach(a=>{let o=t.createSubContext(e.options);r&&o.delayNextStep(r),R(this,a,o),i=Math.max(i,o.currentTimeline.currentTime),s.push(o.currentTimeline)}),s.forEach(a=>t.currentTimeline.mergeTimelineCollectedStyles(a)),t.transformIntoNewTimeline(i),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){let s=e.strValue,i=t.params?se(s,t.params,t.errors):s;return ue(i,t.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){let s=t.currentAnimateTimings=this._visitTiming(e.timings,t),i=t.currentTimeline;s.delay&&(t.incrementTime(s.delay),i.snapshotCurrentStyles());let r=e.style;r.type==_.Keyframes?this.visitKeyframes(r,t):(t.incrementTime(s.duration),this.visitStyle(r,t),i.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){let s=t.currentTimeline,i=t.currentAnimateTimings;!i&&s.hasCurrentStyleProperties()&&s.forwardFrame();let r=i&&i.easing||e.easing;e.isEmptyStep?s.applyEmptyStep(r):s.setStyles(e.styles,r,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){let s=t.currentAnimateTimings,i=t.currentTimeline.duration,r=s.duration,o=t.createSubContext().currentTimeline;o.easing=s.easing,e.styles.forEach(l=>{let u=l.offset||0;o.forwardTime(u*r),o.setStyles(l.styles,l.easing,t.errors,t.options),o.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(o),t.transformIntoNewTimeline(i+r),t.previousNode=e}visitQuery(e,t){let s=t.currentTimeline.currentTime,i=e.options||{},r=i.delay?V(i.delay):0;r&&(t.previousNode.type===_.Style||s==0&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=Ne);let a=s,o=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!i.optional,t.errors);t.currentQueryTotal=o.length;let l=null;o.forEach((u,c)=>{t.currentQueryIndex=c;let h=t.createSubContext(e.options,u);r&&h.delayNextStep(r),u===t.element&&(l=h.currentTimeline),R(this,e.animation,h),h.currentTimeline.applyStylesToKeyframe();let S=h.currentTimeline.currentTime;a=Math.max(a,S)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(a),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){let s=t.parentContext,i=t.currentTimeline,r=e.timings,a=Math.abs(r.duration),o=a*(t.currentQueryTotal-1),l=a*t.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=o-l;break;case"full":l=s.currentStaggerTime;break}let c=t.currentTimeline;l&&c.delayNextStep(l);let h=c.currentTime;R(this,e.animation,t),t.previousNode=e,s.currentStaggerTime=i.currentTime-h+(i.startTime-s.currentTimeline.startTime)}},Ne={},nt=class n{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=Ne;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(e,t,s,i,r,a,o,l){this._driver=e,this.element=t,this.subInstructions=s,this._enterClassName=i,this._leaveClassName=r,this.errors=a,this.timelines=o,this.currentTimeline=l||new Ce(this._driver,t,0),o.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;let s=e,i=this.options;s.duration!=null&&(i.duration=V(s.duration)),s.delay!=null&&(i.delay=V(s.delay));let r=s.params;if(r){let a=i.params;a||(a=this.options.params={}),Object.keys(r).forEach(o=>{(!t||!a.hasOwnProperty(o))&&(a[o]=se(r[o],a,this.errors))})}}_copyOptions(){let e={};if(this.options){let t=this.options.params;if(t){let s=e.params={};Object.keys(t).forEach(i=>{s[i]=t[i]})}}return e}createSubContext(e=null,t,s){let i=t||this.element,r=new n(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,s||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(e),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(e){return this.previousNode=Ne,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,s){let i={duration:t??e.duration,delay:this.currentTimeline.currentTime+(s??0)+e.delay,easing:""},r=new rt(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,i,e.stretchStartingKeyframe);return this.timelines.push(r),i}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,s,i,r,a){let o=[];if(i&&o.push(this.element),e.length>0){e=e.replace(Xs,"."+this._enterClassName),e=e.replace(Js,"."+this._leaveClassName);let l=s!=1,u=this._driver.query(this.element,e,l);s!==0&&(u=s<0?u.slice(u.length+s,u.length):u.slice(0,s)),o.push(...u)}return!r&&o.length==0&&a.push(It(t)),o}},Ce=class n{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(e,t,s,i){this._driver=e,this.element=t,this.startTime=s,this._elementTimelineStylesLookup=i,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let t=this._keyframes.size===1&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new n(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=Hs,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,s]of this._globalTimelineStyles)this._backFill.set(t,s||z),this._currentKeyframe.set(t,z);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,s,i){t&&this._previousKeyframe.set("easing",t);let r=i&&i.params||{},a=Zs(e,this._globalTimelineStyles);for(let[o,l]of a){let u=se(l,r,s);this._pendingStyles.set(o,u),this._localTimelineStyles.has(o)||this._backFill.set(o,this._globalTimelineStyles.get(o)??z),this._updateStyle(o,u)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,s)=>{let i=this._styleSummary.get(s);(!i||t.time>i.time)&&this._updateStyle(s,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,t=new Set,s=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((o,l)=>{let u=new Map([...this._backFill,...o]);u.forEach((c,h)=>{c===re?e.add(h):c===z&&t.add(h)}),s||u.set("offset",l/this.duration),i.push(u)});let r=[...e.values()],a=[...t.values()];if(s){let o=i[0],l=new Map(o);o.set("offset",0),l.set("offset",1),i=[o,l]}return mt(this.element,i,r,a,this.duration,this.startTime,this.easing,!1)}},rt=class extends Ce{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(e,t,s,i,r,a,o=!1){super(e,t,a.delay),this.keyframes=s,this.preStyleProps=i,this.postStyleProps=r,this._stretchStartingKeyframe=o,this.timings={duration:a.duration,delay:a.delay,easing:a.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:s,easing:i}=this.timings;if(this._stretchStartingKeyframe&&t){let r=[],a=s+t,o=t/a,l=new Map(e[0]);l.set("offset",0),r.push(l);let u=new Map(e[0]);u.set("offset",as(o)),r.push(u);let c=e.length-1;for(let h=1;h<=c;h++){let S=new Map(e[h]),y=S.get("offset"),d=t+y*s;S.set("offset",as(d/a)),r.push(S)}s=a,t=0,i="",e=r}return mt(this.element,e,this.preStyleProps,this.postStyleProps,s,t,i,!0)}};function as(n,e=3){let t=Math.pow(10,e-1);return Math.round(n*t)/t}function Zs(n,e){let t=new Map,s;return n.forEach(i=>{if(i==="*"){s??=e.keys();for(let r of s)t.set(r,z)}else for(let[r,a]of i)t.set(r,a)}),t}function os(n,e,t,s,i,r,a,o,l,u,c,h,S){return{type:0,element:n,triggerName:e,isRemovalTransition:i,fromState:t,fromStyles:r,toState:s,toStyles:a,timelines:o,queriedElements:l,preStyleProps:u,postStyleProps:c,totalTime:h,errors:S}}var He={},De=class{_triggerName;ast;_stateStyles;constructor(e,t,s){this._triggerName=e,this.ast=t,this._stateStyles=s}match(e,t,s,i){return ei(this.ast.matchers,e,t,s,i)}buildStyles(e,t,s){let i=this._stateStyles.get("*");return e!==void 0&&(i=this._stateStyles.get(e?.toString())||i),i?i.buildStyles(t,s):new Map}build(e,t,s,i,r,a,o,l,u,c){let h=[],S=this.ast.options&&this.ast.options.params||He,y=o&&o.params||He,d=this.buildStyles(s,y,h),g=l&&l.params||He,v=this.buildStyles(i,g,h),w=new Set,P=new Map,D=new Map,N=i==="void",J={params:_s(g,S),delay:this.ast.options?.delay},B=c?[]:pt(e,t,this.ast.animation,r,a,d,v,J,u,h),M=0;return B.forEach(k=>{M=Math.max(k.duration+k.delay,M)}),h.length?os(t,this._triggerName,s,i,N,d,v,[],[],P,D,M,h):(B.forEach(k=>{let W=k.element,Z=F(P,W,new Set);k.preStyleProps.forEach(H=>Z.add(H));let gt=F(D,W,new Set);k.postStyleProps.forEach(H=>gt.add(H)),W!==t&&w.add(W)}),os(t,this._triggerName,s,i,N,d,v,B,[...w.values()],P,D,M))}};function ei(n,e,t,s,i){return n.some(r=>r(e,t,s,i))}function _s(n,e){let t=me({},e);return Object.entries(n).forEach(([s,i])=>{i!=null&&(t[s]=i)}),t}var at=class{styles;defaultParams;normalizer;constructor(e,t,s){this.styles=e,this.defaultParams=t,this.normalizer=s}buildStyles(e,t){let s=new Map,i=_s(e,this.defaultParams);return this.styles.styles.forEach(r=>{typeof r!="string"&&r.forEach((a,o)=>{a&&(a=se(a,i,t));let l=this.normalizer.normalizePropertyName(o,t);a=this.normalizer.normalizeStyleValue(o,l,a,t),s.set(o,a)})}),s}};function ti(n,e,t){return new ot(n,e,t)}var ot=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(e,t,s){this.name=e,this.ast=t,this._normalizer=s,t.states.forEach(i=>{let r=i.options&&i.options.params||{};this.states.set(i.name,new at(i.style,r,s))}),ls(this.states,"true","1"),ls(this.states,"false","0"),t.transitions.forEach(i=>{this.transitionFactories.push(new De(e,i,this.states))}),this.fallbackTransition=si(e,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,s,i){return this.transitionFactories.find(a=>a.match(e,t,s,i))||null}matchStyles(e,t,s){return this.fallbackTransition.buildStyles(e,t,s)}};function si(n,e,t){let s=[(a,o)=>!0],i={type:_.Sequence,steps:[],options:null},r={type:_.Transition,animation:i,matchers:s,options:null,queryCount:0,depCount:0};return new De(n,r,e)}function ls(n,e,t){n.has(e)?n.has(t)||n.set(t,n.get(e)):n.has(t)&&n.set(e,n.get(t))}var ii=new ne,lt=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s}register(e,t){let s=[],i=[],r=dt(this._driver,t,s,i);if(s.length)throw Vt(s);this._animations.set(e,r)}_buildPlayer(e,t,s){let i=e.element,r=qe(this._normalizer,e.keyframes,t,s);return this._driver.animate(i,r,e.duration,e.delay,e.easing,[],!0)}create(e,t,s={}){let i=[],r=this._animations.get(e),a,o=new Map;if(r?(a=pt(this._driver,t,r,_e,ae,new Map,new Map,s,ii,i),a.forEach(c=>{let h=F(o,c.element,new Map);c.postStyleProps.forEach(S=>h.set(S,null))})):(i.push($t()),a=[]),i.length)throw Ut(i);o.forEach((c,h)=>{c.forEach((S,y)=>{c.set(y,this._driver.computeStyle(h,y,z))})});let l=a.map(c=>{let h=o.get(c.element);return this._buildPlayer(c,new Map,h)}),u=U(l);return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){let t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);let s=this.players.indexOf(t);s>=0&&this.players.splice(s,1)}_getPlayer(e){let t=this._playersById.get(e);if(!t)throw Gt(e);return t}listen(e,t,s,i){let r=ge(t,"","","");return pe(this._getPlayer(e),s,r,i),()=>{}}command(e,t,s,i){if(s=="register"){this.register(e,i[0]);return}if(s=="create"){let a=i[0]||{};this.create(e,t,a);return}let r=this._getPlayer(e);switch(s){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(e);break}}},us="ng-animate-queued",ni=".ng-animate-queued",Ye="ng-animate-disabled",ri=".ng-animate-disabled",ai="ng-star-inserted",oi=".ng-star-inserted",li=[],Ss={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},ui={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},q="__ng_removed",ce=class{namespaceId;value;options;get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;let s=e&&e.hasOwnProperty("value"),i=s?e.value:e;if(this.value=ci(i),s){let r=e,{value:a}=r,o=Et(r,["value"]);this.options=o}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let t=e.params;if(t){let s=this.options.params;Object.keys(t).forEach(i=>{s[i]==null&&(s[i]=t[i])})}}},he="void",Xe=new ce(he),ut=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(e,t,s){this.id=e,this.hostElement=t,this._engine=s,this._hostClassName="ng-tns-"+e,I(t,this._hostClassName)}listen(e,t,s,i){if(!this._triggers.has(t))throw jt(s,t);if(s==null||s.length==0)throw Wt(t);if(!fi(s))throw Ht(s,t);let r=F(this._elementListeners,e,[]),a={name:t,phase:s,callback:i};r.push(a);let o=F(this._engine.statesByElement,e,new Map);return o.has(t)||(I(e,oe),I(e,oe+"-"+t),o.set(t,Xe)),()=>{this._engine.afterFlush(()=>{let l=r.indexOf(a);l>=0&&r.splice(l,1),this._triggers.has(t)||o.delete(t)})}}register(e,t){return this._triggers.has(e)?!1:(this._triggers.set(e,t),!0)}_getTrigger(e){let t=this._triggers.get(e);if(!t)throw Yt(e);return t}trigger(e,t,s,i=!0){let r=this._getTrigger(t),a=new fe(this.id,t,e),o=this._engine.statesByElement.get(e);o||(I(e,oe),I(e,oe+"-"+t),this._engine.statesByElement.set(e,o=new Map));let l=o.get(t),u=new ce(s,this.id);if(!(s&&s.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),o.set(t,u),l||(l=Xe),!(u.value===he)&&l.value===u.value){if(!pi(l.params,u.params)){let g=[],v=r.matchStyles(l.value,l.params,g),w=r.matchStyles(u.value,u.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{j(e,v),K(e,w)})}return}let S=F(this._engine.playersByElement,e,[]);S.forEach(g=>{g.namespaceId==this.id&&g.triggerName==t&&g.queued&&g.destroy()});let y=r.matchTransition(l.value,u.value,e,u.params),d=!1;if(!y){if(!i)return;y=r.fallbackTransition,d=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:y,fromState:l,toState:u,player:a,isFallbackTransition:d}),d||(I(e,us),a.onStart(()=>{ie(e,us)})),a.onDone(()=>{let g=this.players.indexOf(a);g>=0&&this.players.splice(g,1);let v=this._engine.playersByElement.get(e);if(v){let w=v.indexOf(a);w>=0&&v.splice(w,1)}}),this.players.push(a),S.push(a),a}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,s)=>{this._elementListeners.set(s,t.filter(i=>i.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let t=this._engine.playersByElement.get(e);t&&(t.forEach(s=>s.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){let s=this._engine.driver.query(e,le,!0);s.forEach(i=>{if(i[q])return;let r=this._engine.fetchNamespacesByElement(i);r.size?r.forEach(a=>a.triggerLeaveAnimation(i,t,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>s.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(e,t,s,i){let r=this._engine.statesByElement.get(e),a=new Map;if(r){let o=[];if(r.forEach((l,u)=>{if(a.set(u,l.value),this._triggers.has(u)){let c=this.trigger(e,u,he,i);c&&o.push(c)}}),o.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,a),s&&U(o).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let t=this._elementListeners.get(e),s=this._engine.statesByElement.get(e);if(t&&s){let i=new Set;t.forEach(r=>{let a=r.name;if(i.has(a))return;i.add(a);let l=this._triggers.get(a).fallbackTransition,u=s.get(a)||Xe,c=new ce(he),h=new fe(this.id,a,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:a,transition:l,fromState:u,toState:c,player:h,isFallbackTransition:!0})})}}removeNode(e,t){let s=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let i=!1;if(s.totalAnimations){let r=s.players.length?s.playersByQueriedElement.get(e):[];if(r&&r.length)i=!0;else{let a=e;for(;a=a.parentNode;)if(s.statesByElement.get(a)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(e),i)s.markElementAsRemoved(this.id,e,!1,t);else{let r=e[q];(!r||r===Ss)&&(s.afterFlush(()=>this.clearElementCache(e)),s.destroyInnerAnimations(e),s._onRemovalComplete(e,t))}}insertNode(e,t){I(e,this._hostClassName)}drainQueuedTransitions(e){let t=[];return this._queue.forEach(s=>{let i=s.player;if(i.destroyed)return;let r=s.element,a=this._elementListeners.get(r);a&&a.forEach(o=>{if(o.name==s.triggerName){let l=ge(r,s.triggerName,s.fromState.value,s.toState.value);l._data=e,pe(s.player,o.phase,l,o.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):t.push(s)}),this._queue=[],t.sort((s,i)=>{let r=s.transition.ast.depCount,a=i.transition.ast.depCount;return r==0||a==0?r-a:this._engine.driver.containsElement(s.element,i.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},ht=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(e,t)=>{};_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,s){this.bodyNode=e,this.driver=t,this._normalizer=s}get queuedPlayers(){let e=[];return this._namespaceList.forEach(t=>{t.players.forEach(s=>{s.queued&&e.push(s)})}),e}createNamespace(e,t){let s=new ut(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(s,t):(this.newHostElements.set(t,s),this.collectEnterElement(t)),this._namespaceLookup[e]=s}_balanceNamespaceList(e,t){let s=this._namespaceList,i=this.namespacesByHostElement;if(s.length-1>=0){let a=!1,o=this.driver.getParentElement(t);for(;o;){let l=i.get(o);if(l){let u=s.indexOf(l);s.splice(u+1,0,e),a=!0;break}o=this.driver.getParentElement(o)}a||s.unshift(e)}else s.push(e);return i.set(t,e),e}register(e,t){let s=this._namespaceLookup[e];return s||(s=this.createNamespace(e,t)),s}registerTrigger(e,t,s){let i=this._namespaceLookup[e];i&&i.register(t,s)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let s=this._fetchNamespace(e);this.namespacesByHostElement.delete(s.hostElement);let i=this._namespaceList.indexOf(s);i>=0&&this._namespaceList.splice(i,1),s.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let t=new Set,s=this.statesByElement.get(e);if(s){for(let i of s.values())if(i.namespaceId){let r=this._fetchNamespace(i.namespaceId);r&&t.add(r)}}return t}trigger(e,t,s,i){if(we(t)){let r=this._fetchNamespace(e);if(r)return r.trigger(t,s,i),!0}return!1}insertNode(e,t,s,i){if(!we(t))return;let r=t[q];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;let a=this.collectedLeaveElements.indexOf(t);a>=0&&this.collectedLeaveElements.splice(a,1)}if(e){let a=this._fetchNamespace(e);a&&a.insertNode(t,s)}i&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),I(e,Ye)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),ie(e,Ye))}removeNode(e,t,s){if(we(t)){let i=e?this._fetchNamespace(e):null;i?i.removeNode(t,s):this.markElementAsRemoved(e,t,!1,s);let r=this.namespacesByHostElement.get(t);r&&r.id!==e&&r.removeNode(t,s)}else this._onRemovalComplete(t,s)}markElementAsRemoved(e,t,s,i,r){this.collectedLeaveElements.push(t),t[q]={namespaceId:e,setForRemoval:i,hasAnimation:s,removedBeforeQueried:!1,previousTriggersValues:r}}listen(e,t,s,i,r){return we(t)?this._fetchNamespace(e).listen(t,s,i,r):()=>{}}_buildInstruction(e,t,s,i,r){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,s,i,e.fromState.options,e.toState.options,t,r)}destroyInnerAnimations(e){let t=this.driver.query(e,le,!0);t.forEach(s=>this.destroyActiveAnimationsForElement(s)),this.playersByQueriedElement.size!=0&&(t=this.driver.query(e,Se,!0),t.forEach(s=>this.finishActiveQueriedAnimationOnElement(s)))}destroyActiveAnimationsForElement(e){let t=this.playersByElement.get(e);t&&t.forEach(s=>{s.queued?s.markedForDestroy=!0:s.destroy()})}finishActiveQueriedAnimationOnElement(e){let t=this.playersByQueriedElement.get(e);t&&t.forEach(s=>s.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return U(this.players).onDone(()=>e());e()})}processLeaveNode(e){let t=e[q];if(t&&t.setForRemoval){if(e[q]=Ss,t.namespaceId){this.destroyInnerAnimations(e);let s=this._fetchNamespace(t.namespaceId);s&&s.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(Ye)&&this.markElementAsDisabled(e,!1),this.driver.query(e,ri,!0).forEach(s=>{this.markElementAsDisabled(s,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((s,i)=>this._balanceNamespaceList(s,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let s=0;s<this.collectedEnterElements.length;s++){let i=this.collectedEnterElements[s];I(i,ai)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let s=[];try{t=this._flushAnimations(s,e)}finally{for(let i=0;i<s.length;i++)s[i]()}}else for(let s=0;s<this.collectedLeaveElements.length;s++){let i=this.collectedLeaveElements[s];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(s=>s()),this._flushFns=[],this._whenQuietFns.length){let s=this._whenQuietFns;this._whenQuietFns=[],t.length?U(t).onDone(()=>{s.forEach(i=>i())}):s.forEach(i=>i())}}reportError(e){throw Xt(e)}_flushAnimations(e,t){let s=new ne,i=[],r=new Map,a=[],o=new Map,l=new Map,u=new Map,c=new Set;this.disabledNodes.forEach(f=>{c.add(f);let m=this.driver.query(f,ni,!0);for(let p=0;p<m.length;p++)c.add(m[p])});let h=this.bodyNode,S=Array.from(this.statesByElement.keys()),y=fs(S,this.collectedEnterElements),d=new Map,g=0;y.forEach((f,m)=>{let p=_e+g++;d.set(m,p),f.forEach(T=>I(T,p))});let v=[],w=new Set,P=new Set;for(let f=0;f<this.collectedLeaveElements.length;f++){let m=this.collectedLeaveElements[f],p=m[q];p&&p.setForRemoval&&(v.push(m),w.add(m),p.hasAnimation?this.driver.query(m,oi,!0).forEach(T=>w.add(T)):P.add(m))}let D=new Map,N=fs(S,Array.from(w));N.forEach((f,m)=>{let p=ae+g++;D.set(m,p),f.forEach(T=>I(T,p))}),e.push(()=>{y.forEach((f,m)=>{let p=d.get(m);f.forEach(T=>ie(T,p))}),N.forEach((f,m)=>{let p=D.get(m);f.forEach(T=>ie(T,p))}),v.forEach(f=>{this.processLeaveNode(f)})});let J=[],B=[];for(let f=this._namespaceList.length-1;f>=0;f--)this._namespaceList[f].drainQueuedTransitions(t).forEach(p=>{let T=p.player,A=p.element;if(J.push(T),this.collectedEnterElements.length){let C=A[q];if(C&&C.setForMove){if(C.previousTriggersValues&&C.previousTriggersValues.has(p.triggerName)){let Y=C.previousTriggersValues.get(p.triggerName),L=this.statesByElement.get(p.element);if(L&&L.has(p.triggerName)){let de=L.get(p.triggerName);de.value=Y,L.set(p.triggerName,de)}}T.destroy();return}}let Q=!h||!this.driver.containsElement(h,A),O=D.get(A),G=d.get(A),b=this._buildInstruction(p,s,G,O,Q);if(b.errors&&b.errors.length){B.push(b);return}if(Q){T.onStart(()=>j(A,b.fromStyles)),T.onDestroy(()=>K(A,b.toStyles)),i.push(T);return}if(p.isFallbackTransition){T.onStart(()=>j(A,b.fromStyles)),T.onDestroy(()=>K(A,b.toStyles)),i.push(T);return}let St=[];b.timelines.forEach(C=>{C.stretchStartingKeyframe=!0,this.disabledNodes.has(C.element)||St.push(C)}),b.timelines=St,s.append(A,b.timelines);let vs={instruction:b,player:T,element:A};a.push(vs),b.queriedElements.forEach(C=>F(o,C,[]).push(T)),b.preStyleProps.forEach((C,Y)=>{if(C.size){let L=l.get(Y);L||l.set(Y,L=new Set),C.forEach((de,Oe)=>L.add(Oe))}}),b.postStyleProps.forEach((C,Y)=>{let L=u.get(Y);L||u.set(Y,L=new Set),C.forEach((de,Oe)=>L.add(Oe))})});if(B.length){let f=[];B.forEach(m=>{f.push(xt(m.triggerName,m.errors))}),J.forEach(m=>m.destroy()),this.reportError(f)}let M=new Map,k=new Map;a.forEach(f=>{let m=f.element;s.has(m)&&(k.set(m,m),this._beforeAnimationBuild(f.player.namespaceId,f.instruction,M))}),i.forEach(f=>{let m=f.element;this._getPreviousPlayers(m,!1,f.namespaceId,f.triggerName,null).forEach(T=>{F(M,m,[]).push(T),T.destroy()})});let W=v.filter(f=>ds(f,l,u)),Z=new Map;cs(Z,this.driver,P,u,z).forEach(f=>{ds(f,l,u)&&W.push(f)});let H=new Map;y.forEach((f,m)=>{cs(H,this.driver,new Set(f),l,re)}),W.forEach(f=>{let m=Z.get(f),p=H.get(f);Z.set(f,new Map([...m?.entries()??[],...p?.entries()??[]]))});let Re=[],yt=[],_t={};a.forEach(f=>{let{element:m,player:p,instruction:T}=f;if(s.has(m)){if(c.has(m)){p.onDestroy(()=>K(m,T.toStyles)),p.disabled=!0,p.overrideTotalTime(T.totalTime),i.push(p);return}let A=_t;if(k.size>1){let O=m,G=[];for(;O=O.parentNode;){let b=k.get(O);if(b){A=b;break}G.push(O)}G.forEach(b=>k.set(b,A))}let Q=this._buildAnimation(p.namespaceId,T,M,r,H,Z);if(p.setRealPlayer(Q),A===_t)Re.push(p);else{let O=this.playersByElement.get(A);O&&O.length&&(p.parentPlayer=U(O)),i.push(p)}}else j(m,T.fromStyles),p.onDestroy(()=>K(m,T.toStyles)),yt.push(p),c.has(m)&&i.push(p)}),yt.forEach(f=>{let m=r.get(f.element);if(m&&m.length){let p=U(m);f.setRealPlayer(p)}}),i.forEach(f=>{f.parentPlayer?f.syncPlayerEvents(f.parentPlayer):f.destroy()});for(let f=0;f<v.length;f++){let m=v[f],p=m[q];if(ie(m,ae),p&&p.hasAnimation)continue;let T=[];if(o.size){let Q=o.get(m);Q&&Q.length&&T.push(...Q);let O=this.driver.query(m,Se,!0);for(let G=0;G<O.length;G++){let b=o.get(O[G]);b&&b.length&&T.push(...b)}}let A=T.filter(Q=>!Q.destroyed);A.length?di(this,m,A):this.processLeaveNode(m)}return v.length=0,Re.forEach(f=>{this.players.push(f),f.onDone(()=>{f.destroy();let m=this.players.indexOf(f);this.players.splice(m,1)}),f.play()}),Re}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,s,i,r){let a=[];if(t){let o=this.playersByQueriedElement.get(e);o&&(a=o)}else{let o=this.playersByElement.get(e);if(o){let l=!r||r==he;o.forEach(u=>{u.queued||!l&&u.triggerName!=i||a.push(u)})}}return(s||i)&&(a=a.filter(o=>!(s&&s!=o.namespaceId||i&&i!=o.triggerName))),a}_beforeAnimationBuild(e,t,s){let i=t.triggerName,r=t.element,a=t.isRemovalTransition?void 0:e,o=t.isRemovalTransition?void 0:i;for(let l of t.timelines){let u=l.element,c=u!==r,h=F(s,u,[]);this._getPreviousPlayers(u,c,a,o,t.toState).forEach(y=>{let d=y.getRealPlayer();d.beforeDestroy&&d.beforeDestroy(),y.destroy(),h.push(y)})}j(r,t.fromStyles)}_buildAnimation(e,t,s,i,r,a){let o=t.triggerName,l=t.element,u=[],c=new Set,h=new Set,S=t.timelines.map(d=>{let g=d.element;c.add(g);let v=g[q];if(v&&v.removedBeforeQueried)return new $(d.duration,d.delay);let w=g!==l,P=mi((s.get(g)||li).map(M=>M.getRealPlayer())).filter(M=>{let k=M;return k.element?k.element===g:!1}),D=r.get(g),N=a.get(g),J=qe(this._normalizer,d.keyframes,D,N),B=this._buildPlayer(d,J,P);if(d.subTimeline&&i&&h.add(g),w){let M=new fe(e,o,g);M.setRealPlayer(B),u.push(M)}return B});u.forEach(d=>{F(this.playersByQueriedElement,d.element,[]).push(d),d.onDone(()=>hi(this.playersByQueriedElement,d.element,d))}),c.forEach(d=>I(d,Ue));let y=U(S);return y.onDestroy(()=>{c.forEach(d=>ie(d,Ue)),K(l,t.toStyles)}),h.forEach(d=>{F(i,d,[]).push(y)}),y}_buildPlayer(e,t,s){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,s):new $(e.duration,e.delay)}},fe=class{namespaceId;triggerName;element;_player=new $;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(e,t,s){this.namespaceId=e,this.triggerName=t,this.element=s}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,s)=>{t.forEach(i=>pe(e,s,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){F(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let t=this._player;t.triggerCallback&&t.triggerCallback(e)}};function hi(n,e,t){let s=n.get(e);if(s){if(s.length){let i=s.indexOf(t);s.splice(i,1)}s.length==0&&n.delete(e)}return s}function ci(n){return n??null}function we(n){return n&&n.nodeType===1}function fi(n){return n=="start"||n=="done"}function hs(n,e){let t=n.style.display;return n.style.display=e??"none",t}function cs(n,e,t,s,i){let r=[];t.forEach(l=>r.push(hs(l)));let a=[];s.forEach((l,u)=>{let c=new Map;l.forEach(h=>{let S=e.computeStyle(u,h,i);c.set(h,S),(!S||S.length==0)&&(u[q]=ui,a.push(u))}),n.set(u,c)});let o=0;return t.forEach(l=>hs(l,r[o++])),a}function fs(n,e){let t=new Map;if(n.forEach(o=>t.set(o,[])),e.length==0)return t;let s=1,i=new Set(e),r=new Map;function a(o){if(!o)return s;let l=r.get(o);if(l)return l;let u=o.parentNode;return t.has(u)?l=u:i.has(u)?l=s:l=a(u),r.set(o,l),l}return e.forEach(o=>{let l=a(o);l!==s&&t.get(l).push(o)}),t}function I(n,e){n.classList?.add(e)}function ie(n,e){n.classList?.remove(e)}function di(n,e,t){U(t).onDone(()=>n.processLeaveNode(e))}function mi(n){let e=[];return Es(n,e),e}function Es(n,e){for(let t=0;t<n.length;t++){let s=n[t];s instanceof ee?Es(s.players,e):e.push(s)}}function pi(n,e){let t=Object.keys(n),s=Object.keys(e);if(t.length!=s.length)return!1;for(let i=0;i<t.length;i++){let r=t[i];if(!e.hasOwnProperty(r)||n[r]!==e[r])return!1}return!0}function ds(n,e,t){let s=t.get(n);if(!s)return!1;let i=e.get(n);return i?s.forEach(r=>i.add(r)):e.set(n,s),t.delete(n),!0}var Me=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(e,t)=>{};constructor(e,t,s){this._driver=t,this._normalizer=s,this._transitionEngine=new ht(e.body,t,s),this._timelineEngine=new lt(e.body,t,s),this._transitionEngine.onRemovalComplete=(i,r)=>this.onRemovalComplete(i,r)}registerTrigger(e,t,s,i,r){let a=e+"-"+i,o=this._triggerCache[a];if(!o){let l=[],u=[],c=dt(this._driver,r,l,u);if(l.length)throw Qt(i,l);o=ti(i,c,this._normalizer),this._triggerCache[a]=o}this._transitionEngine.registerTrigger(t,i,o)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,s,i){this._transitionEngine.insertNode(e,t,s,i)}onRemove(e,t,s){this._transitionEngine.removeNode(e,t,s)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,s,i){if(s.charAt(0)=="@"){let[r,a]=Be(s),o=i;this._timelineEngine.command(r,t,a,o)}else this._transitionEngine.trigger(e,t,s,i)}listen(e,t,s,i,r){if(s.charAt(0)=="@"){let[a,o]=Be(s);return this._timelineEngine.listen(a,t,o,r)}return this._transitionEngine.listen(e,t,s,i,r)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function gi(n,e){let t=null,s=null;return Array.isArray(e)&&e.length?(t=xe(e[0]),e.length>1&&(s=xe(e[e.length-1]))):e instanceof Map&&(t=xe(e)),t||s?new yi(n,t,s):null}var yi=(()=>{class n{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(t,s,i){this._element=t,this._startStyles=s,this._endStyles=i;let r=n.initialStylesByElement.get(t);r||n.initialStylesByElement.set(t,r=new Map),this._initialStyles=r}start(){this._state<1&&(this._startStyles&&K(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(K(this._element,this._initialStyles),this._endStyles&&(K(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(n.initialStylesByElement.delete(this._element),this._startStyles&&(j(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(j(this._element,this._endStyles),this._endStyles=null),K(this._element,this._initialStyles),this._state=3)}}return n})();function xe(n){let e=null;return n.forEach((t,s)=>{_i(s)&&(e=e||new Map,e.set(s,t))}),e}function _i(n){return n==="display"||n==="position"}var ke=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(e,t,s,i){this.element=e,this.keyframes=t,this.options=s,this._specialStyles=i,this._duration=s.duration,this._delay=s.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let t=[];return e.forEach(s=>{t.push(Object.fromEntries(s))}),t}_triggerWebAnimation(e,t,s){return e.animate(this._convertKeyframesToObject(t),s)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((s,i)=>{i!=="offset"&&e.set(i,this._finished?s:Te(this.element,i))}),this.currentSnapshot=e}triggerCallback(e){let t=e==="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},ct=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,t){return Qe(e,t)}getParentElement(e){return ye(e)}query(e,t,s){return Ve(e,t,s)}computeStyle(e,t,s){return Te(e,t)}animate(e,t,s,i,r,a=[]){let o=i==0?"both":"forwards",l={duration:s,delay:i,fill:o};r&&(l.easing=r);let u=new Map,c=a.filter(y=>y instanceof ke);ts(s,i)&&c.forEach(y=>{y.currentSnapshot.forEach((d,g)=>u.set(g,d))});let h=Zt(t).map(y=>new Map(y));h=ss(e,h,u);let S=gi(e,h);return new ke(e,h,l,S)}};function Ci(n,e){return n==="noop"?new Me(e,new gs,new Ze):new Me(e,new ct,new et)}var ms=class{_driver;_animationAst;constructor(e,t){this._driver=e;let s=[],r=dt(e,t,s,[]);if(s.length)throw qt(s);this._animationAst=r}buildTimelines(e,t,s,i,r){let a=Array.isArray(t)?Ge(t):t,o=Array.isArray(s)?Ge(s):s,l=[];r=r||new ne;let u=pt(this._driver,e,this._animationAst,_e,ae,a,o,i,r,l);if(l.length)throw Bt(l);return u}},Pe="@",Ts="@.disabled",Fe=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(e,t,s,i){this.namespaceId=e,this.delegate=t,this.engine=s,this._onDestroy=i}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,s,i=!0){this.delegate.insertBefore(e,t,s),this.engine.onInsert(this.namespaceId,t,e,i)}removeChild(e,t,s){this.parentNode(t)&&this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,s,i){this.delegate.setAttribute(e,t,s,i)}removeAttribute(e,t,s){this.delegate.removeAttribute(e,t,s)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,s,i){this.delegate.setStyle(e,t,s,i)}removeStyle(e,t,s){this.delegate.removeStyle(e,t,s)}setProperty(e,t,s){t.charAt(0)==Pe&&t==Ts?this.disableAnimations(e,!!s):this.delegate.setProperty(e,t,s)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,s,i){return this.delegate.listen(e,t,s,i)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}},ft=class extends Fe{factory;constructor(e,t,s,i,r){super(t,s,i,r),this.factory=e,this.namespaceId=t}setProperty(e,t,s){t.charAt(0)==Pe?t.charAt(1)=="."&&t==Ts?(s=s===void 0?!0:!!s,this.disableAnimations(e,s)):this.engine.process(this.namespaceId,e,t.slice(1),s):this.delegate.setProperty(e,t,s)}listen(e,t,s,i){if(t.charAt(0)==Pe){let r=Si(e),a=t.slice(1),o="";return a.charAt(0)!=Pe&&([a,o]=Ei(a)),this.engine.listen(this.namespaceId,r,a,o,l=>{let u=l._data||-1;this.factory.scheduleListenerCallback(u,s,l)})}return this.delegate.listen(e,t,s,i)}};function Si(n){switch(n){case"body":return document.body;case"document":return document;case"window":return window;default:return n}}function Ei(n){let e=n.indexOf("."),t=n.substring(0,e),s=n.slice(e+1);return[t,s]}var ps=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(e,t,s){this.delegate=e,this.engine=t,this._zone=s,t.onRemovalComplete=(i,r)=>{r?.removeChild(null,i)}}createRenderer(e,t){let s="",i=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){let u=this._rendererCache,c=u.get(i);if(!c){let h=()=>u.delete(i);c=new Fe(s,i,this.engine,h),u.set(i,c)}return c}let r=t.id,a=t.id+"-"+this._currentId;this._currentId++,this.engine.register(a,e);let o=u=>{Array.isArray(u)?u.forEach(o):this.engine.registerTrigger(r,a,e,u.name,u)};return t.data.animation.forEach(o),new ft(this,a,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,s){if(e>=0&&e<this._microtaskId){this._zone.run(()=>t(s));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(r=>{let[a,o]=r;a(o)}),this._animationCallbacksBuffer=[]})}),i.push([t,s])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(e){this.engine.flush(),this.delegate.componentReplaced?.(e)}};export{is as AnimationDriver,gs as NoopAnimationDriver,ms as \u0275Animation,Me as \u0275AnimationEngine,ft as \u0275AnimationRenderer,ps as \u0275AnimationRendererFactory,Je as \u0275AnimationStyleNormalizer,Fe as \u0275BaseAnimationRenderer,_e as \u0275ENTER_CLASSNAME,ae as \u0275LEAVE_CLASSNAME,Ze as \u0275NoopAnimationStyleNormalizer,fe as \u0275TransitionAnimationPlayer,ct as \u0275WebAnimationsDriver,ke as \u0275WebAnimationsPlayer,et as \u0275WebAnimationsStyleNormalizer,ts as \u0275allowPreviousPlayerStylesMerge,Ks as \u0275camelCaseToDashCase,Qe as \u0275containsElement,Ci as \u0275createEngine,ye as \u0275getParentElement,Ve as \u0275invokeQuery,Zt as \u0275normalizeKeyframes,Jt as \u0275validateStyleProperty,Fs as \u0275validateWebAnimatableStyleProperty};
