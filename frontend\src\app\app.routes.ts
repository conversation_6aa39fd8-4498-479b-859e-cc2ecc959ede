import { Routes } from '@angular/router';
import { LoginComponent } from './components/auth/login/login.component';
import { RegisterComponent } from './components/auth/register/register.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { SearchComponent } from './components/search/search.component';
import { PredictionsComponent } from './components/predictions/predictions.component';
import { StockDetailComponent } from './components/stock-detail/stock-detail.component';

export const routes: Routes = [
    { path: '', redirectTo: '/login', pathMatch: 'full' },
    { path: 'login', component: LoginComponent },
    { path: 'register', component: RegisterComponent },
    { path: 'dashboard', component: DashboardComponent },
    { path: 'search', component: SearchComponent },
    { path: 'predictions', component: PredictionsComponent },
    { path: 'stock/:symbol/:exchange', component: StockDetailComponent },
    { path: '**', redirectTo: '/login' }
];
