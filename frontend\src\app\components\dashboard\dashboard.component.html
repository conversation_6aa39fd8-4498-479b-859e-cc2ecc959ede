<div class="dashboard-container">
  <!-- Header -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="header-text">
        <h1>Stock Prediction Dashboard</h1>
        <p *ngIf="user">Welcome back, {{ user.firstName }}!</p>
      </div>
      <div class="header-actions">
        <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-menu-button">
          <mat-icon>account_circle</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu">
          <div class="user-info">
            <div class="user-details" *ngIf="user">
              <strong>{{ user.firstName }} {{ user.lastName }}</strong>
              <span class="user-email">{{ user.email }}</span>
            </div>
          </div>
          <mat-divider></mat-divider>
          <button mat-menu-item routerLink="/profile">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item routerLink="/settings">
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()" class="logout-button">
            <mat-icon>logout</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>
      </div>
    </div>
  </div>

  <!-- Quick Stats Cards -->
  <div class="stats-grid">
    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <mat-icon class="stat-icon">trending_up</mat-icon>
          <div class="stat-info">
            <h3>{{ watchlistStocks.length }}</h3>
            <p>Watchlist Stocks</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <mat-icon class="stat-icon">analytics</mat-icon>
          <div class="stat-info">
            <h3>{{ recentPredictions.length }}</h3>
            <p>Active Predictions</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <mat-icon class="stat-icon">account_balance</mat-icon>
          <div class="stat-info">
            <h3>{{ user?.subscription?.plan | titlecase }}</h3>
            <p>Subscription Plan</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Main Content Tabs -->
  <mat-tab-group class="main-tabs">
    <!-- Watchlist Tab -->
    <mat-tab label="My Watchlist">
      <div class="tab-content">
        <div class="section-header">
          <h2>Your Watchlist</h2>
          <button mat-raised-button color="primary" routerLink="/search">
            <mat-icon>add</mat-icon>
            Add Stocks
          </button>
        </div>

        <mat-card>
          <mat-card-content>
            <div *ngIf="loading.watchlist" class="loading-container">
              <mat-spinner></mat-spinner>
            </div>

            <div *ngIf="!loading.watchlist && watchlistStocks.length === 0" class="empty-state">
              <mat-icon>trending_up</mat-icon>
              <h3>No stocks in your watchlist</h3>
              <p>Add some stocks to start tracking their performance</p>
              <button mat-raised-button color="primary" routerLink="/search">
                Browse Stocks
              </button>
            </div>

            <table mat-table [dataSource]="watchlistStocks" *ngIf="!loading.watchlist && watchlistStocks.length > 0">
              <ng-container matColumnDef="symbol">
                <th mat-header-cell *matHeaderCellDef>Symbol</th>
                <td mat-cell *matCellDef="let stock">
                  <div class="stock-symbol">
                    <strong>{{ stock.symbol }}</strong>
                    <span class="exchange-badge">{{ stock.exchange }}</span>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Company</th>
                <td mat-cell *matCellDef="let stock">{{ stock.name }}</td>
              </ng-container>

              <ng-container matColumnDef="currentPrice">
                <th mat-header-cell *matHeaderCellDef>Price</th>
                <td mat-cell *matCellDef="let stock">{{ formatCurrency(stock.currentPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="dayChange">
                <th mat-header-cell *matHeaderCellDef>Change</th>
                <td mat-cell *matCellDef="let stock">
                  <span [style.color]="getChangeColor(stock.dayChange)">
                    {{ formatCurrency(stock.dayChange) }}
                  </span>
                </td>
              </ng-container>

              <ng-container matColumnDef="dayChangePercent">
                <th mat-header-cell *matHeaderCellDef>Change %</th>
                <td mat-cell *matCellDef="let stock">
                  <mat-chip [style.background-color]="getChangeColor(stock.dayChangePercent)" [style.color]="'white'">
                    {{ formatPercent(stock.dayChangePercent) }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let stock">
                  <button mat-icon-button color="primary" (click)="generatePrediction(stock)"
                    matTooltip="Generate Prediction">
                    <mat-icon>analytics</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="removeFromWatchlist(stock)"
                    matTooltip="Remove from Watchlist">
                    <mat-icon>remove</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Recent Predictions Tab -->
    <mat-tab label="Recent Predictions">
      <div class="tab-content">
        <div class="section-header">
          <h2>Recent Predictions</h2>
          <button mat-raised-button color="primary" routerLink="/predictions">
            View All
          </button>
        </div>

        <mat-card>
          <mat-card-content>
            <div *ngIf="loading.predictions" class="loading-container">
              <mat-spinner></mat-spinner>
            </div>

            <div *ngIf="!loading.predictions && recentPredictions.length === 0" class="empty-state">
              <mat-icon>analytics</mat-icon>
              <h3>No predictions yet</h3>
              <p>Generate your first prediction to see AI-powered insights</p>
            </div>

            <table mat-table [dataSource]="recentPredictions"
              *ngIf="!loading.predictions && recentPredictions.length > 0">
              <ng-container matColumnDef="stockSymbol">
                <th mat-header-cell *matHeaderCellDef>Stock</th>
                <td mat-cell *matCellDef="let prediction">{{ prediction.stockSymbol }}</td>
              </ng-container>

              <ng-container matColumnDef="predictionType">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip>{{ prediction.predictionType | titlecase }}</mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="currentPrice">
                <th mat-header-cell *matHeaderCellDef>Current</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.currentPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="predictedPrice">
                <th mat-header-cell *matHeaderCellDef>Predicted</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.predictedPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="confidence">
                <th mat-header-cell *matHeaderCellDef>Confidence</th>
                <td mat-cell *matCellDef="let prediction">{{ prediction.confidence }}%</td>
              </ng-container>

              <ng-container matColumnDef="direction">
                <th mat-header-cell *matHeaderCellDef>Direction</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getDirectionColor(prediction.direction)" [style.color]="'white'">
                    {{ prediction.direction | titlecase }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="targetDate">
                <th mat-header-cell *matHeaderCellDef>Target Date</th>
                <td mat-cell *matCellDef="let prediction">{{ formatDate(prediction.targetDate) }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="predictionColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: predictionColumns;"></tr>
            </table>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Market Overview Tab -->
    <mat-tab label="Market Overview">
      <div class="tab-content">
        <div class="market-overview">
          <!-- Top Gainers -->
          <mat-card class="market-section">
            <mat-card-header>
              <mat-card-title>Top Gainers</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div *ngIf="loading.movers" class="loading-container">
                <mat-spinner></mat-spinner>
              </div>
              <div class="stock-list" *ngIf="!loading.movers">
                <div class="stock-item" *ngFor="let stock of topGainers">
                  <div class="stock-info">
                    <strong>{{ stock.symbol }}</strong>
                    <span class="stock-price">{{ formatCurrency(stock.currentPrice) }}</span>
                  </div>
                  <div class="stock-change positive">
                    {{ formatPercent(stock.dayChangePercent) }}
                  </div>
                  <button mat-icon-button (click)="addToWatchlist(stock)" *ngIf="!isInWatchlist(stock)"
                    matTooltip="Add to Watchlist">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Top Losers -->
          <mat-card class="market-section">
            <mat-card-header>
              <mat-card-title>Top Losers</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div *ngIf="loading.movers" class="loading-container">
                <mat-spinner></mat-spinner>
              </div>
              <div class="stock-list" *ngIf="!loading.movers">
                <div class="stock-item" *ngFor="let stock of topLosers">
                  <div class="stock-info">
                    <strong>{{ stock.symbol }}</strong>
                    <span class="stock-price">{{ formatCurrency(stock.currentPrice) }}</span>
                  </div>
                  <div class="stock-change negative">
                    {{ formatPercent(stock.dayChangePercent) }}
                  </div>
                  <button mat-icon-button (click)="addToWatchlist(stock)" *ngIf="!isInWatchlist(stock)"
                    matTooltip="Add to Watchlist">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>