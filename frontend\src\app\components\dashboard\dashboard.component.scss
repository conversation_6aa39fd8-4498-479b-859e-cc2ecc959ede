.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
  }

  .header-text {
    h1 {
      margin: 0;
      color: #1976d2;
      font-size: 2.5rem;
      font-weight: 300;
    }

    p {
      margin: 5px 0 0 0;
      color: #666;
      font-size: 1.1rem;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;

    .user-menu-button {
      width: 48px;
      height: 48px;

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 15px;

    .stat-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: #1976d2;
    }

    .stat-info {
      h3 {
        margin: 0;
        font-size: 2rem;
        font-weight: 500;
        color: #333;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
      }
    }
  }
}

.main-tabs {
  .mat-mdc-tab-body-wrapper {
    padding-top: 20px;
  }
}

.tab-content {
  min-height: 400px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #333;
    font-weight: 400;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    margin-bottom: 20px;
    color: #ccc;
  }

  h3 {
    margin: 0 0 10px 0;
    font-weight: 400;
  }

  p {
    margin: 0 0 20px 0;
  }
}

.stock-symbol {
  display: flex;
  align-items: center;
  gap: 8px;

  .exchange-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
  }
}

.market-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.market-section {
  .stock-list {
    .stock-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .stock-info {
        display: flex;
        flex-direction: column;
        gap: 4px;

        strong {
          font-size: 0.9rem;
        }

        .stock-price {
          font-size: 0.8rem;
          color: #666;
        }
      }

      .stock-change {
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;

        &.positive {
          background: #e8f5e8;
          color: #2e7d32;
        }

        &.negative {
          background: #ffebee;
          color: #c62828;
        }
      }
    }
  }
}

// Table styles
.mat-mdc-table {
  width: 100%;

  .mat-mdc-header-cell {
    font-weight: 500;
    color: #333;
  }

  .mat-mdc-cell {
    padding: 12px 8px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .dashboard-header {
    h1 {
      font-size: 2rem;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  // Hide some table columns on mobile
  .mat-mdc-table {

    .mat-column-name,
    .mat-column-dayChange {
      display: none;
    }
  }
}

// Material Design overrides
.mat-mdc-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.mat-mdc-chip {
  font-size: 0.8rem;
  min-height: 24px;
}

.mat-mdc-raised-button {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

// User menu styles
.user-info {
  padding: 16px;

  .user-details {
    display: flex;
    flex-direction: column;
    gap: 4px;

    strong {
      font-size: 16px;
      color: #333;
    }

    .user-email {
      font-size: 14px;
      color: #666;
    }
  }
}

.logout-button {
  color: #f44336 !important;

  mat-icon {
    color: #f44336 !important;
  }
}