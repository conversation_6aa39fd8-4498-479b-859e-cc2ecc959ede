import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { Stock, Prediction, User } from '../../models/stock.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  user: User | null = null;
  watchlistStocks: Stock[] = [];
  recentPredictions: Prediction[] = [];
  topGainers: Stock[] = [];
  topLosers: Stock[] = [];
  popularStocks: Stock[] = [];

  loading = {
    watchlist: false,
    predictions: false,
    movers: false,
    popular: false
  };

  displayedColumns: string[] = ['symbol', 'name', 'currentPrice', 'dayChange', 'dayChangePercent', 'actions'];
  predictionColumns: string[] = ['stockSymbol', 'predictionType', 'currentPrice', 'predictedPrice', 'confidence', 'direction', 'targetDate'];

  constructor(
    private apiService: ApiService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.apiService.currentUser$.subscribe(user => {
      this.user = user;
      if (user) {
        this.loadDashboardData();
      }
    });
  }

  loadDashboardData(): void {
    this.loadWatchlist();
    this.loadRecentPredictions();
    this.loadTopMovers();
    this.loadPopularStocks();
  }

  loadWatchlist(): void {
    this.loading.watchlist = true;
    this.apiService.getWatchlistData().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.watchlistStocks = response.data;
        }
        this.loading.watchlist = false;
      },
      error: (error) => {
        console.error('Error loading watchlist:', error);
        this.loading.watchlist = false;
      }
    });
  }

  loadRecentPredictions(): void {
    this.loading.predictions = true;
    this.apiService.getRecentPredictions(10).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.recentPredictions = response.data;
        }
        this.loading.predictions = false;
      },
      error: (error) => {
        console.error('Error loading predictions:', error);
        this.loading.predictions = false;
      }
    });
  }

  loadTopMovers(): void {
    this.loading.movers = true;

    // Load top gainers
    this.apiService.getTopMovers('gainers', 5).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.topGainers = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading top gainers:', error);
      }
    });

    // Load top losers
    this.apiService.getTopMovers('losers', 5).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.topLosers = response.data;
        }
        this.loading.movers = false;
      },
      error: (error) => {
        console.error('Error loading top losers:', error);
        this.loading.movers = false;
      }
    });
  }

  loadPopularStocks(): void {
    this.loading.popular = true;
    this.apiService.getPopularStocks(10).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.popularStocks = response.data;
        }
        this.loading.popular = false;
      },
      error: (error) => {
        console.error('Error loading popular stocks:', error);
        this.loading.popular = false;
      }
    });
  }

  removeFromWatchlist(stock: Stock): void {
    this.apiService.removeFromWatchlist(stock.symbol, stock.exchange).subscribe({
      next: (response) => {
        if (response.success) {
          this.watchlistStocks = this.watchlistStocks.filter(s =>
            !(s.symbol === stock.symbol && s.exchange === stock.exchange)
          );
        }
      },
      error: (error) => {
        console.error('Error removing from watchlist:', error);
      }
    });
  }

  addToWatchlist(stock: Stock): void {
    this.apiService.addToWatchlist(stock.symbol, stock.exchange).subscribe({
      next: (response) => {
        if (response.success) {
          // Refresh watchlist
          this.loadWatchlist();
        }
      },
      error: (error) => {
        console.error('Error adding to watchlist:', error);
      }
    });
  }

  generatePrediction(stock: Stock): void {
    this.apiService.generatePrediction({
      symbol: stock.symbol,
      exchange: stock.exchange,
      timeframe: '1d',
      predictionType: 'short_term'
    }).subscribe({
      next: (response) => {
        if (response.success) {
          // Refresh predictions
          this.loadRecentPredictions();
        }
      },
      error: (error) => {
        console.error('Error generating prediction:', error);
      }
    });
  }

  getChangeColor(change: number): string {
    if (change > 0) return 'green';
    if (change < 0) return 'red';
    return 'gray';
  }

  getDirectionColor(direction: string): string {
    switch (direction) {
      case 'bullish': return 'green';
      case 'bearish': return 'red';
      default: return 'orange';
    }
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(value);
  }

  formatPercent(value: number): string {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString('en-IN');
  }

  isInWatchlist(stock: Stock): boolean {
    return this.watchlistStocks.some(w =>
      w.symbol === stock.symbol && w.exchange === stock.exchange
    );
  }

  logout(): void {
    this.apiService.logout().subscribe({
      next: () => {
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        // Even if logout fails on server, clear local storage and redirect
        this.router.navigate(['/login']);
      }
    });
  }
}
