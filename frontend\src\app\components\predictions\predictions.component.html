<div class="predictions-container">
  <!-- Header -->
  <div class="predictions-header">
    <h1>AI Predictions</h1>
    <button mat-raised-button routerLink="/dashboard">
      <mat-icon>arrow_back</mat-icon>
      Back to Dashboard
    </button>
  </div>

  <!-- Summary Cards -->
  <div class="summary-grid">
    <mat-card class="summary-card">
      <mat-card-content>
        <div class="summary-content">
          <mat-icon class="summary-icon active">analytics</mat-icon>
          <div class="summary-info">
            <h3>{{ activePredictions.length }}</h3>
            <p>Active Predictions</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="summary-card">
      <mat-card-content>
        <div class="summary-content">
          <mat-icon class="summary-icon completed">check_circle</mat-icon>
          <div class="summary-info">
            <h3>{{ completedPredictions.length }}</h3>
            <p>Completed Predictions</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="summary-card">
      <mat-card-content>
        <div class="summary-content">
          <mat-icon class="summary-icon total">timeline</mat-icon>
          <div class="summary-info">
            <h3>{{ allPredictions.length }}</h3>
            <p>Total Predictions</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Predictions Tabs -->
  <mat-tab-group class="predictions-tabs">
    <!-- All Predictions Tab -->
    <mat-tab label="All Predictions">
      <div class="tab-content">
        <mat-card>
          <mat-card-header>
            <mat-card-title>All Predictions</mat-card-title>
            <mat-card-subtitle>Complete history of your AI predictions</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="loading.all" class="loading-container">
              <mat-spinner></mat-spinner>
              <p>Loading predictions...</p>
            </div>

            <div *ngIf="!loading.all && allPredictions.length === 0" class="empty-state">
              <mat-icon>analytics</mat-icon>
              <h3>No predictions yet</h3>
              <p>Start by adding stocks to your watchlist and generate predictions</p>
              <button mat-raised-button color="primary" routerLink="/search">
                Browse Stocks
              </button>
            </div>

            <table mat-table [dataSource]="allPredictions" *ngIf="!loading.all && allPredictions.length > 0">
              <ng-container matColumnDef="stockSymbol">
                <th mat-header-cell *matHeaderCellDef>Stock</th>
                <td mat-cell *matCellDef="let prediction">
                  <strong>{{ prediction.stockSymbol }}</strong>
                </td>
              </ng-container>

              <ng-container matColumnDef="predictionType">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip>{{ prediction.predictionType | titlecase }}</mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="currentPrice">
                <th mat-header-cell *matHeaderCellDef>Current</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.currentPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="predictedPrice">
                <th mat-header-cell *matHeaderCellDef>Predicted</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.predictedPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="confidence">
                <th mat-header-cell *matHeaderCellDef>Confidence</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getAccuracyColor(prediction.confidence)" [style.color]="'white'">
                    {{ prediction.confidence }}%
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="direction">
                <th mat-header-cell *matHeaderCellDef>Direction</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getDirectionColor(prediction.direction)" [style.color]="'white'">
                    {{ prediction.direction | titlecase }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="targetDate">
                <th mat-header-cell *matHeaderCellDef>Target Date</th>
                <td mat-cell *matCellDef="let prediction">{{ formatDate(prediction.targetDate) }}</td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getStatusColor(prediction.status)" [style.color]="'white'">
                    {{ prediction.status | titlecase }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let prediction">
                  <button mat-icon-button color="primary" (click)="refreshPrediction(prediction)"
                    matTooltip="Refresh Prediction" *ngIf="prediction.status === 'active'">
                    <mat-icon>refresh</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deletePrediction(prediction)"
                    matTooltip="Delete Prediction">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Active Predictions Tab -->
    <mat-tab label="Active Predictions">
      <div class="tab-content">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Active Predictions</mat-card-title>
            <mat-card-subtitle>Predictions currently being tracked</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="loading.active" class="loading-container">
              <mat-spinner></mat-spinner>
              <p>Loading active predictions...</p>
            </div>

            <div *ngIf="!loading.active && activePredictions.length === 0" class="empty-state">
              <mat-icon>analytics</mat-icon>
              <h3>No active predictions</h3>
              <p>Generate new predictions from your watchlist</p>
              <button mat-raised-button color="primary" routerLink="/dashboard">
                Go to Dashboard
              </button>
            </div>

            <table mat-table [dataSource]="activePredictions" *ngIf="!loading.active && activePredictions.length > 0">
              <ng-container matColumnDef="stockSymbol">
                <th mat-header-cell *matHeaderCellDef>Stock</th>
                <td mat-cell *matCellDef="let prediction">
                  <strong>{{ prediction.stockSymbol }}</strong>
                </td>
              </ng-container>

              <ng-container matColumnDef="predictionType">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip>{{ prediction.predictionType | titlecase }}</mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="currentPrice">
                <th mat-header-cell *matHeaderCellDef>Current</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.currentPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="predictedPrice">
                <th mat-header-cell *matHeaderCellDef>Predicted</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.predictedPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="confidence">
                <th mat-header-cell *matHeaderCellDef>Confidence</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getAccuracyColor(prediction.confidence)" [style.color]="'white'">
                    {{ prediction.confidence }}%
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="direction">
                <th mat-header-cell *matHeaderCellDef>Direction</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getDirectionColor(prediction.direction)" [style.color]="'white'">
                    {{ prediction.direction | titlecase }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="targetDate">
                <th mat-header-cell *matHeaderCellDef>Target Date</th>
                <td mat-cell *matCellDef="let prediction">{{ formatDate(prediction.targetDate) }}</td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Potential Return</th>
                <td mat-cell *matCellDef="let prediction">
                  <span [style.color]="getDirectionColor(prediction.direction)">
                    {{ calculatePotentialReturn(prediction) | number:'1.2-2' }}%
                  </span>
                </td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let prediction">
                  <button mat-icon-button color="primary" (click)="refreshPrediction(prediction)"
                    matTooltip="Refresh Prediction">
                    <mat-icon>refresh</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deletePrediction(prediction)"
                    matTooltip="Delete Prediction">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Completed Predictions Tab -->
    <mat-tab label="Completed Predictions">
      <div class="tab-content">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Completed Predictions</mat-card-title>
            <mat-card-subtitle>Historical predictions and their outcomes</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div *ngIf="loading.completed" class="loading-container">
              <mat-spinner></mat-spinner>
              <p>Loading completed predictions...</p>
            </div>

            <div *ngIf="!loading.completed && completedPredictions.length === 0" class="empty-state">
              <mat-icon>history</mat-icon>
              <h3>No completed predictions</h3>
              <p>Completed predictions will appear here once they reach their target dates</p>
            </div>

            <table mat-table [dataSource]="completedPredictions" *ngIf="!loading.completed && completedPredictions.length > 0">
              <ng-container matColumnDef="stockSymbol">
                <th mat-header-cell *matHeaderCellDef>Stock</th>
                <td mat-cell *matCellDef="let prediction">
                  <strong>{{ prediction.stockSymbol }}</strong>
                </td>
              </ng-container>

              <ng-container matColumnDef="predictionType">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip>{{ prediction.predictionType | titlecase }}</mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="currentPrice">
                <th mat-header-cell *matHeaderCellDef>Initial Price</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.currentPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="predictedPrice">
                <th mat-header-cell *matHeaderCellDef>Predicted</th>
                <td mat-cell *matCellDef="let prediction">{{ formatCurrency(prediction.predictedPrice) }}</td>
              </ng-container>

              <ng-container matColumnDef="confidence">
                <th mat-header-cell *matHeaderCellDef>Confidence</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getAccuracyColor(prediction.confidence)" [style.color]="'white'">
                    {{ prediction.confidence }}%
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="direction">
                <th mat-header-cell *matHeaderCellDef>Direction</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getDirectionColor(prediction.direction)" [style.color]="'white'">
                    {{ prediction.direction | titlecase }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="targetDate">
                <th mat-header-cell *matHeaderCellDef>Target Date</th>
                <td mat-cell *matCellDef="let prediction">{{ formatDate(prediction.targetDate) }}</td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Accuracy</th>
                <td mat-cell *matCellDef="let prediction">
                  <mat-chip [style.background-color]="getAccuracyColor(prediction.accuracy || 0)" [style.color]="'white'">
                    {{ prediction.accuracy || 'N/A' }}%
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let prediction">
                  <button mat-icon-button color="warn" (click)="deletePrediction(prediction)"
                    matTooltip="Delete Prediction">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
