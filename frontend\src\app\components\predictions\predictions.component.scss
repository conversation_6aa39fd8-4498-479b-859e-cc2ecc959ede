.predictions-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.predictions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h1 {
    margin: 0;
    color: #333;
  }
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  .summary-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .summary-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      
      &.active {
        color: #2196f3;
      }
      
      &.completed {
        color: #4caf50;
      }
      
      &.total {
        color: #ff9800;
      }
    }
    
    .summary-info {
      h3 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
        color: #333;
      }
      
      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 0.9rem;
      }
    }
  }
}

.predictions-tabs {
  .tab-content {
    padding: 20px 0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  
  mat-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: #666;
    margin: 0;
  }
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  h3 {
    margin: 0 0 8px 0;
    color: #333;
  }
  
  p {
    margin: 0 0 20px 0;
  }
}

table {
  width: 100%;
  
  th {
    font-weight: 600;
    color: #333;
    background-color: #f5f5f5;
  }
  
  td {
    padding: 12px 8px;
    border-bottom: 1px solid #eee;
  }
  
  tr:hover {
    background-color: #f9f9f9;
  }
}

mat-chip {
  font-size: 0.8rem;
  font-weight: 500;
}

.mat-mdc-button {
  margin: 0 4px;
}

// Responsive design
@media (max-width: 768px) {
  .predictions-container {
    padding: 16px;
  }
  
  .predictions-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    
    h1 {
      font-size: 1.5rem;
    }
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .summary-card {
    .summary-content {
      .summary-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
      
      .summary-info {
        h3 {
          font-size: 1.4rem;
        }
      }
    }
  }
  
  table {
    font-size: 0.8rem;
    
    th, td {
      padding: 8px 4px;
    }
  }
  
  // Hide some columns on mobile
  .mat-column-predictionType,
  .mat-column-confidence {
    display: none;
  }
}

@media (max-width: 480px) {
  .mat-column-currentPrice,
  .mat-column-targetDate {
    display: none;
  }
}
