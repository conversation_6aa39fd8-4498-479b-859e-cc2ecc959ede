import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { ApiService } from '../../services/api.service';
import { Prediction } from '../../models/stock.model';

@Component({
  selector: 'app-predictions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatTabsModule,
    MatSelectModule,
    MatFormFieldModule,
    MatSnackBarModule
  ],
  templateUrl: './predictions.component.html',
  styleUrls: ['./predictions.component.scss']
})
export class PredictionsComponent implements OnInit {
  allPredictions: Prediction[] = [];
  activePredictions: Prediction[] = [];
  completedPredictions: Prediction[] = [];

  loading = {
    all: false,
    active: false,
    completed: false
  };

  displayedColumns: string[] = [
    'stockSymbol',
    'predictionType',
    'currentPrice',
    'predictedPrice',
    'confidence',
    'direction',
    'targetDate',
    'status',
    'actions'
  ];

  constructor(
    private apiService: ApiService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadAllPredictions();
    this.loadActivePredictions();
    this.loadCompletedPredictions();
  }

  loadAllPredictions(): void {
    this.loading.all = true;
    this.apiService.getAllPredictions().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.allPredictions = response.data;
        }
        this.loading.all = false;
      },
      error: (error) => {
        console.error('Error loading all predictions:', error);
        this.loading.all = false;
      }
    });
  }

  loadActivePredictions(): void {
    this.loading.active = true;
    this.apiService.getActivePredictions().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.activePredictions = response.data;
        }
        this.loading.active = false;
      },
      error: (error) => {
        console.error('Error loading active predictions:', error);
        this.loading.active = false;
      }
    });
  }

  loadCompletedPredictions(): void {
    this.loading.completed = true;
    this.apiService.getCompletedPredictions().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.completedPredictions = response.data;
        }
        this.loading.completed = false;
      },
      error: (error) => {
        console.error('Error loading completed predictions:', error);
        this.loading.completed = false;
      }
    });
  }

  deletePrediction(prediction: Prediction): void {
    if (!prediction._id) {
      this.snackBar.open('Invalid prediction ID', 'Close', { duration: 3000 });
      return;
    }

    if (confirm(`Are you sure you want to delete the prediction for ${prediction.stockSymbol}?`)) {
      this.apiService.deletePrediction(prediction._id).subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Prediction deleted successfully', 'Close', {
              duration: 3000
            });
            // Refresh all lists
            this.loadAllPredictions();
            this.loadActivePredictions();
            this.loadCompletedPredictions();
          }
        },
        error: (error) => {
          console.error('Error deleting prediction:', error);
          this.snackBar.open('Error deleting prediction. Please try again.', 'Close', {
            duration: 3000
          });
        }
      });
    }
  }

  refreshPrediction(prediction: Prediction): void {
    if (!prediction._id) {
      this.snackBar.open('Invalid prediction ID', 'Close', { duration: 3000 });
      return;
    }

    this.apiService.refreshPrediction(prediction._id).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Prediction refreshed successfully', 'Close', {
            duration: 3000
          });
          // Refresh all lists
          this.loadAllPredictions();
          this.loadActivePredictions();
          this.loadCompletedPredictions();
        }
      },
      error: (error) => {
        console.error('Error refreshing prediction:', error);
        this.snackBar.open('Error refreshing prediction. Please try again.', 'Close', {
          duration: 3000
        });
      }
    });
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(value);
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString('en-IN');
  }

  getDirectionColor(direction: string): string {
    switch (direction) {
      case 'bullish': return '#4caf50';
      case 'bearish': return '#f44336';
      default: return '#ff9800';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'active': return '#2196f3';
      case 'completed': return '#4caf50';
      case 'expired': return '#9e9e9e';
      default: return '#ff9800';
    }
  }

  getAccuracyColor(accuracy: number): string {
    if (accuracy >= 80) return '#4caf50';
    if (accuracy >= 60) return '#ff9800';
    return '#f44336';
  }

  calculatePotentialReturn(prediction: Prediction): number {
    if (!prediction.currentPrice || !prediction.predictedPrice) return 0;
    return ((prediction.predictedPrice - prediction.currentPrice) / prediction.currentPrice) * 100;
  }
}
