<div class="search-container">
  <!-- Header -->
  <div class="search-header">
    <h1>Browse & Search Stocks</h1>
    <button mat-raised-button routerLink="/dashboard">
      <mat-icon>arrow_back</mat-icon>
      Back to Dashboard
    </button>
  </div>

  <!-- Search Section -->
  <mat-card class="search-card">
    <mat-card-header>
      <mat-card-title>Search Stocks</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="search-form">
        <mat-form-field appearance="outline" class="search-input">
          <mat-label>Search by symbol or company name</mat-label>
          <input matInput 
                 [(ngModel)]="searchQuery" 
                 (keyup.enter)="searchStocks()"
                 placeholder="e.g., RELIANCE, TCS, HDFC">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="exchange-select">
          <mat-label>Exchange</mat-label>
          <mat-select [(ngModel)]="selectedExchange">
            <mat-option value="NSE">NSE</mat-option>
            <mat-option value="BSE">BSE</mat-option>
          </mat-select>
        </mat-form-field>

        <div class="search-actions">
          <button mat-raised-button color="primary" (click)="searchStocks()" [disabled]="loading.search">
            <mat-icon>search</mat-icon>
            Search
          </button>
          <button mat-button (click)="clearSearch()" *ngIf="searchResults.length > 0">
            <mat-icon>clear</mat-icon>
            Clear
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Search Results -->
  <mat-card *ngIf="searchResults.length > 0 || loading.search" class="results-card">
    <mat-card-header>
      <mat-card-title>Search Results</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="loading.search" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Searching stocks...</p>
      </div>

      <div *ngIf="!loading.search && searchResults.length === 0 && searchQuery" class="empty-state">
        <mat-icon>search_off</mat-icon>
        <h3>No stocks found</h3>
        <p>Try searching with different keywords or check the spelling</p>
      </div>

      <table mat-table [dataSource]="searchResults" *ngIf="!loading.search && searchResults.length > 0">
        <ng-container matColumnDef="symbol">
          <th mat-header-cell *matHeaderCellDef>Symbol</th>
          <td mat-cell *matCellDef="let stock">
            <div class="stock-symbol">
              <strong>{{ stock.symbol }}</strong>
              <span class="exchange-badge">{{ stock.exchange }}</span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Company</th>
          <td mat-cell *matCellDef="let stock">{{ stock.name }}</td>
        </ng-container>

        <ng-container matColumnDef="exchange">
          <th mat-header-cell *matHeaderCellDef>Exchange</th>
          <td mat-cell *matCellDef="let stock">
            <mat-chip>{{ stock.exchange }}</mat-chip>
          </td>
        </ng-container>

        <ng-container matColumnDef="currentPrice">
          <th mat-header-cell *matHeaderCellDef>Price</th>
          <td mat-cell *matCellDef="let stock">
            {{ stock.currentPrice ? formatCurrency(stock.currentPrice) : 'N/A' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="dayChange">
          <th mat-header-cell *matHeaderCellDef>Change %</th>
          <td mat-cell *matCellDef="let stock">
            <span *ngIf="stock.dayChangePercent" 
                  [style.color]="getChangeColor(stock.dayChangePercent)">
              {{ formatPercent(stock.dayChangePercent) }}
            </span>
            <span *ngIf="!stock.dayChangePercent">N/A</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let stock">
            <button mat-icon-button color="primary" (click)="addToWatchlist(stock)"
              matTooltip="Add to Watchlist">
              <mat-icon>add</mat-icon>
            </button>
            <button mat-icon-button color="accent" (click)="generatePrediction(stock)"
              matTooltip="Generate Prediction">
              <mat-icon>analytics</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </mat-card-content>
  </mat-card>

  <!-- Popular Stocks -->
  <mat-card class="popular-card">
    <mat-card-header>
      <mat-card-title>Popular Stocks</mat-card-title>
      <mat-card-subtitle>Most traded stocks on NSE</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="loading.popular" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading popular stocks...</p>
      </div>

      <table mat-table [dataSource]="popularStocks" *ngIf="!loading.popular && popularStocks.length > 0">
        <ng-container matColumnDef="symbol">
          <th mat-header-cell *matHeaderCellDef>Symbol</th>
          <td mat-cell *matCellDef="let stock">
            <div class="stock-symbol">
              <strong>{{ stock.symbol }}</strong>
              <span class="exchange-badge">{{ stock.exchange }}</span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Company</th>
          <td mat-cell *matCellDef="let stock">{{ stock.name }}</td>
        </ng-container>

        <ng-container matColumnDef="exchange">
          <th mat-header-cell *matHeaderCellDef>Exchange</th>
          <td mat-cell *matCellDef="let stock">
            <mat-chip>{{ stock.exchange }}</mat-chip>
          </td>
        </ng-container>

        <ng-container matColumnDef="currentPrice">
          <th mat-header-cell *matHeaderCellDef>Price</th>
          <td mat-cell *matCellDef="let stock">{{ formatCurrency(stock.currentPrice) }}</td>
        </ng-container>

        <ng-container matColumnDef="dayChange">
          <th mat-header-cell *matHeaderCellDef>Change %</th>
          <td mat-cell *matCellDef="let stock">
            <mat-chip [style.background-color]="getChangeColor(stock.dayChangePercent)" [style.color]="'white'">
              {{ formatPercent(stock.dayChangePercent) }}
            </mat-chip>
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let stock">
            <button mat-icon-button color="primary" (click)="addToWatchlist(stock)"
              matTooltip="Add to Watchlist">
              <mat-icon>add</mat-icon>
            </button>
            <button mat-icon-button color="accent" (click)="generatePrediction(stock)"
              matTooltip="Generate Prediction">
              <mat-icon>analytics</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </mat-card-content>
  </mat-card>
</div>
