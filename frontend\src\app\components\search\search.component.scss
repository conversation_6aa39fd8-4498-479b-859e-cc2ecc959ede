.search-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.search-header {
  margin-bottom: 20px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  h1 {
    margin: 0;
    color: #333;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;

    .user-menu-button {
      width: 48px;
      height: 48px;

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
    }
  }
}

.search-card,
.results-card,
.popular-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  flex-wrap: wrap;

  .search-input {
    flex: 1;
    min-width: 300px;
  }

  .exchange-select {
    min-width: 120px;
  }

  .search-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    color: #666;
    margin: 0;
  }
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 8px 0;
    color: #333;
  }

  p {
    margin: 0;
  }
}

.stock-symbol {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .stock-link {
    text-decoration: none;
    color: #1976d2;
    font-weight: 600;

    &:hover {
      text-decoration: underline;
      color: #1565c0;
    }
  }

  strong {
    font-size: 0.9rem;
  }

  .exchange-badge {
    font-size: 0.7rem;
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 4px;
    width: fit-content;
  }
}

table {
  width: 100%;

  th {
    font-weight: 600;
    color: #333;
  }

  td {
    padding: 12px 8px;
  }
}

mat-chip {
  font-size: 0.8rem;
}

.mat-mdc-button {
  margin: 0 4px;
}

@media (max-width: 768px) {
  .search-container {
    padding: 16px;
  }

  .search-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;

    h1 {
      font-size: 1.5rem;
    }
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;

    .search-input,
    .exchange-select {
      min-width: unset;
    }

    .search-actions {
      justify-content: center;
    }
  }

  table {
    font-size: 0.8rem;

    th,
    td {
      padding: 8px 4px;
    }
  }
}

// User menu styles
.user-info {
  padding: 16px;

  .user-details {
    display: flex;
    flex-direction: column;
    gap: 4px;

    strong {
      font-size: 16px;
      color: #333;
    }

    .user-email {
      font-size: 14px;
      color: #666;
    }
  }
}

.logout-button {
  color: #f44336 !important;

  mat-icon {
    color: #f44336 !important;
  }
}