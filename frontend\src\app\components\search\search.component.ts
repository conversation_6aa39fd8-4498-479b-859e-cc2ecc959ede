import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { Stock, StockSearchResult, User } from '../../models/stock.model';

@Component({
  selector: 'app-search',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatSelectModule,
    MatSnackBarModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule
  ],
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.scss']
})
export class SearchComponent implements OnInit {
  searchQuery = '';
  selectedExchange: 'NSE' | 'BSE' = 'NSE';
  searchResults: StockSearchResult[] = [];
  popularStocks: Stock[] = [];
  user: User | null = null;
  loading = {
    search: false,
    popular: false
  };

  displayedColumns: string[] = ['symbol', 'name', 'exchange', 'currentPrice', 'dayChange', 'actions'];

  constructor(
    private apiService: ApiService,
    private snackBar: MatSnackBar,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadPopularStocks();

    // Subscribe to current user
    this.apiService.currentUser$.subscribe(user => {
      this.user = user;
    });
  }

  searchStocks(): void {
    if (!this.searchQuery || this.searchQuery.length < 2) {
      this.snackBar.open('Please enter at least 2 characters to search', 'Close', {
        duration: 3000
      });
      return;
    }

    this.loading.search = true;
    this.apiService.searchStocks(this.searchQuery, this.selectedExchange).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.searchResults = response.data;
        }
        this.loading.search = false;
      },
      error: (error) => {
        console.error('Error searching stocks:', error);
        this.snackBar.open('Error searching stocks. Please try again.', 'Close', {
          duration: 3000
        });
        this.loading.search = false;
      }
    });
  }

  loadPopularStocks(): void {
    this.loading.popular = true;
    this.apiService.getPopularStocks(20).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.popularStocks = response.data;
        }
        this.loading.popular = false;
      },
      error: (error) => {
        console.error('Error loading popular stocks:', error);
        this.loading.popular = false;
      }
    });
  }

  addToWatchlist(stock: Stock | StockSearchResult): void {
    this.apiService.addToWatchlist(stock.symbol, stock.exchange).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(`${stock.symbol} added to watchlist`, 'Close', {
            duration: 3000
          });
        }
      },
      error: (error) => {
        console.error('Error adding to watchlist:', error);
        this.snackBar.open('Error adding to watchlist. Please try again.', 'Close', {
          duration: 3000
        });
      }
    });
  }

  generatePrediction(stock: Stock | StockSearchResult): void {
    this.apiService.generatePrediction({
      symbol: stock.symbol,
      exchange: stock.exchange,
      timeframe: '1d',
      predictionType: 'short_term'
    }).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open(`Prediction generated for ${stock.symbol}`, 'Close', {
            duration: 3000
          });
        }
      },
      error: (error) => {
        console.error('Error generating prediction:', error);
        this.snackBar.open('Error generating prediction. Please try again.', 'Close', {
          duration: 3000
        });
      }
    });
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.searchResults = [];
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(value);
  }

  formatPercent(value: number): string {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  }

  getChangeColor(change: number): string {
    if (change > 0) return 'green';
    if (change < 0) return 'red';
    return 'gray';
  }

  logout(): void {
    this.apiService.logout().subscribe({
      next: () => {
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        // Even if logout fails on server, clear local storage and redirect
        this.router.navigate(['/login']);
      }
    });
  }
}
