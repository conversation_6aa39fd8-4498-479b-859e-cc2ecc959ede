<div class="stock-detail-container">
  <!-- Header -->
  <div class="stock-header">
    <button mat-icon-button (click)="goBack()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
    </button>

    <div class="stock-info" *ngIf="stock">
      <div class="stock-title">
        <h1>{{ stock.symbol }}</h1>
        <span class="exchange-badge">{{ stock.exchange }}</span>
      </div>
      <h2 class="company-name">{{ stock.name }}</h2>

      <div class="price-info">
        <div class="current-price">{{ formatCurrency(stock.currentPrice) }}</div>
        <div class="price-change" [style.color]="getChangeColor(stock.dayChange)">
          <span>{{ formatCurrency(stock.dayChange) }}</span>
          <span>({{ formatPercent(stock.dayChangePercent) }})</span>
        </div>
      </div>
    </div>

    <div class="header-actions" *ngIf="stock">
      <button mat-raised-button [color]="isInWatchlist ? 'warn' : 'primary'" (click)="toggleWatchlist()">
        <mat-icon>{{ isInWatchlist ? 'remove' : 'add' }}</mat-icon>
        {{ isInWatchlist ? 'Remove from Watchlist' : 'Add to Watchlist' }}
      </button>

      <button mat-raised-button color="accent" (click)="generatePrediction()">
        <mat-icon>analytics</mat-icon>
        Generate Prediction
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading.stock" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading stock details...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!loading.stock && stock" class="main-content">
    <!-- Chart Section -->
    <mat-card class="chart-card">
      <mat-card-header>
        <mat-card-title>Price Chart</mat-card-title>
        <div class="timeframe-selector">
          <mat-button-toggle-group [value]="selectedTimeframe" (change)="onTimeframeChange($event)">
            <mat-button-toggle *ngFor="let tf of timeframes" [value]="tf.value">
              {{ tf.label }}
            </mat-button-toggle>
          </mat-button-toggle-group>
        </div>
      </mat-card-header>

      <mat-card-content>
        <div *ngIf="loading.chart" class="chart-loading">
          <mat-spinner></mat-spinner>
        </div>

        <div *ngIf="!loading.chart && chartData" class="chart-container">
          <canvas #chartCanvas></canvas>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Stock Metrics -->
    <mat-card class="metrics-card" *ngIf="stockMetrics">
      <mat-card-header>
        <mat-card-title>Key Metrics</mat-card-title>
      </mat-card-header>

      <mat-card-content>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">Market Cap</span>
            <span class="metric-value">{{ formatCurrency(stockMetrics.marketCap) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">P/E Ratio</span>
            <span class="metric-value">{{ stockMetrics.peRatio.toFixed(2) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">P/B Ratio</span>
            <span class="metric-value">{{ stockMetrics.pbRatio.toFixed(2) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">Dividend Yield</span>
            <span class="metric-value">{{ stockMetrics.dividendYield.toFixed(2) }}%</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">EPS</span>
            <span class="metric-value">{{ formatCurrency(stockMetrics.eps) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">Book Value</span>
            <span class="metric-value">{{ formatCurrency(stockMetrics.bookValue) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">52W High</span>
            <span class="metric-value">{{ formatCurrency(stockMetrics.week52High) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">52W Low</span>
            <span class="metric-value">{{ formatCurrency(stockMetrics.week52Low) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">Avg Volume</span>
            <span class="metric-value">{{ formatNumber(stockMetrics.avgVolume) }}</span>
          </div>

          <div class="metric-item">
            <span class="metric-label">Beta</span>
            <span class="metric-value">{{ stockMetrics.beta.toFixed(2) }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Predictions Section -->
    <mat-card class="predictions-card">
      <mat-card-header>
        <mat-card-title>AI Predictions</mat-card-title>
      </mat-card-header>

      <mat-card-content>
        <div *ngIf="loading.predictions" class="loading-container">
          <mat-spinner></mat-spinner>
        </div>

        <div *ngIf="!loading.predictions && predictions.length === 0" class="empty-state">
          <mat-icon>analytics</mat-icon>
          <h3>No predictions available</h3>
          <p>Generate your first AI prediction for {{ stock.symbol }}</p>
          <button mat-raised-button color="primary" (click)="generatePrediction()">
            Generate Prediction
          </button>
        </div>

        <div *ngIf="!loading.predictions && predictions.length > 0" class="predictions-list">
          <div class="prediction-item" *ngFor="let prediction of predictions">
            <div class="prediction-header">
              <span class="prediction-type">{{ prediction.predictionType | titlecase }}</span>
              <span class="prediction-date">{{ prediction.createdAt | date:'short' }}</span>
            </div>

            <div class="prediction-details">
              <div class="prediction-prices">
                <div class="price-item">
                  <span class="price-label">Current</span>
                  <span class="price-value">{{ formatCurrency(prediction.currentPrice) }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">Predicted</span>
                  <span class="price-value">{{ formatCurrency(prediction.predictedPrice) }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">Target</span>
                  <span class="price-value">{{ prediction.targetDate | date:'shortDate' }}</span>
                </div>
              </div>

              <div class="prediction-metrics">
                <div class="confidence-meter">
                  <span class="confidence-label">Confidence</span>
                  <mat-progress-bar mode="determinate" [value]="prediction.confidence"
                    [color]="prediction.confidence > 70 ? 'primary' : prediction.confidence > 50 ? 'accent' : 'warn'">
                  </mat-progress-bar>
                  <span class="confidence-value">{{ prediction.confidence }}%</span>
                </div>

                <div class="direction-indicator">
                  <mat-chip
                    [style.background-color]="prediction.direction === 'bullish' ? '#4CAF50' : prediction.direction === 'bearish' ? '#F44336' : '#FF9800'"
                    [style.color]="'white'">
                    <mat-icon>{{ prediction.direction === 'bullish' ? 'trending_up' : prediction.direction === 'bearish'
                      ? 'trending_down' : 'trending_flat' }}</mat-icon>
                    {{ prediction.direction | titlecase }}
                  </mat-chip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>