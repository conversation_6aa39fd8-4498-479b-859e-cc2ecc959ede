.stock-detail-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.stock-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;

  .back-button {
    color: white;
    margin-top: 8px;
  }

  .stock-info {
    flex: 1;

    .stock-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;

      h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 600;
      }

      .exchange-badge {
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
      }
    }

    .company-name {
      margin: 0 0 20px 0;
      font-size: 1.2rem;
      font-weight: 400;
      opacity: 0.9;
    }

    .price-info {
      display: flex;
      align-items: baseline;
      gap: 16px;

      .current-price {
        font-size: 2rem;
        font-weight: 700;
      }

      .price-change {
        font-size: 1.1rem;
        font-weight: 500;
        display: flex;
        gap: 8px;
      }
    }
  }

  .header-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-self: flex-end;

    button {
      min-width: 200px;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

.main-content {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;

  @media (min-width: 1200px) {
    grid-template-columns: 2fr 1fr;
    grid-template-areas: 
      "chart metrics"
      "predictions predictions";
  }
}

.chart-card {
  @media (min-width: 1200px) {
    grid-area: chart;
  }

  mat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .timeframe-selector {
      mat-button-toggle-group {
        border: 1px solid #ddd;
        border-radius: 6px;
      }

      mat-button-toggle {
        border: none;
        font-size: 0.85rem;
        padding: 0 12px;
      }
    }
  }

  .chart-loading {
    display: flex;
    justify-content: center;
    padding: 60px 20px;
  }

  .chart-container {
    height: 400px;
    position: relative;

    canvas {
      max-height: 100%;
      width: 100% !important;
    }
  }
}

.metrics-card {
  @media (min-width: 1200px) {
    grid-area: metrics;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .metric-item {
      display: flex;
      flex-direction: column;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #2196F3;

      .metric-label {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 4px;
      }

      .metric-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
      }
    }
  }
}

.predictions-card {
  @media (min-width: 1200px) {
    grid-area: predictions;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #666;
    }

    p {
      margin: 0 0 20px 0;
      color: #999;
    }
  }

  .predictions-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .prediction-item {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      background: #fafafa;

      .prediction-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .prediction-type {
          font-weight: 600;
          color: #333;
        }

        .prediction-date {
          font-size: 0.9rem;
          color: #666;
        }
      }

      .prediction-details {
        .prediction-prices {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 16px;
          margin-bottom: 16px;

          .price-item {
            display: flex;
            flex-direction: column;

            .price-label {
              font-size: 0.85rem;
              color: #666;
              margin-bottom: 4px;
            }

            .price-value {
              font-weight: 600;
              color: #333;
            }
          }
        }

        .prediction-metrics {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 20px;

          .confidence-meter {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;

            .confidence-label {
              font-size: 0.9rem;
              color: #666;
              min-width: 80px;
            }

            mat-progress-bar {
              flex: 1;
              height: 8px;
              border-radius: 4px;
            }

            .confidence-value {
              font-weight: 600;
              min-width: 40px;
              text-align: right;
            }
          }

          .direction-indicator {
            mat-chip {
              display: flex;
              align-items: center;
              gap: 4px;
              font-weight: 500;

              mat-icon {
                font-size: 18px;
                width: 18px;
                height: 18px;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .stock-header {
    flex-direction: column;
    align-items: stretch;

    .header-actions {
      flex-direction: row;
      align-self: stretch;

      button {
        flex: 1;
        min-width: auto;
      }
    }
  }

  .metrics-card .metrics-grid {
    grid-template-columns: 1fr;
  }

  .prediction-details .prediction-metrics {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}
