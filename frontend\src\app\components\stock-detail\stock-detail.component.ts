import { <PERSON>mponent, On<PERSON>nit, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, interval, takeUntil, switchMap } from 'rxjs';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';
import { ApiService } from '../../services/api.service';
import { Stock, HistoricalData, Prediction, ApiResponse } from '../../models/types';

interface MatButtonToggleChange {
  value: string;
}

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    fill: boolean;
  }[];
}

interface StockMetrics {
  marketCap: number;
  peRatio: number;
  pbRatio: number;
  dividendYield: number;
  eps: number;
  bookValue: number;
  week52High: number;
  week52Low: number;
  avgVolume: number;
  beta: number;
}

@Component({
  selector: 'app-stock-detail',
  templateUrl: './stock-detail.component.html',
  styleUrls: ['./stock-detail.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatButtonToggleModule
  ]
})
export class StockDetailComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('chartCanvas', { static: false }) chartCanvas!: ElementRef<HTMLCanvasElement>;

  private destroy$ = new Subject<void>();
  private chart: Chart | null = null;

  symbol: string = '';
  exchange: 'NSE' | 'BSE' = 'NSE';
  stock: Stock | null = null;
  historicalData: HistoricalData[] = [];
  predictions: Prediction[] = [];
  chartData: ChartData | null = null;

  selectedTimeframe: string = '1M';
  timeframes = [
    { value: '1D', label: '1 Day', period: '1d' },
    { value: '1W', label: '1 Week', period: '5d' },
    { value: '1M', label: '1 Month', period: '1mo' },
    { value: '3M', label: '3 Months', period: '3mo' },
    { value: '6M', label: '6 Months', period: '6mo' },
    { value: '1Y', label: '1 Year', period: '1y' },
    { value: '2Y', label: '2 Years', period: '2y' },
    { value: '5Y', label: '5 Years', period: '5y' }
  ];

  loading = {
    stock: false,
    chart: false,
    predictions: false
  };

  isInWatchlist = false;
  stockMetrics: StockMetrics | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private apiService: ApiService
  ) {
    Chart.register(...registerables);
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.symbol = params['symbol'];
      this.exchange = params['exchange'] || 'NSE';
      this.loadStockData();
      this.startRealTimeUpdates();
    });
  }

  ngAfterViewInit(): void {
    // Chart will be initialized when data is loaded
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.chart) {
      this.chart.destroy();
    }
  }

  loadStockData(): void {
    this.loadStockDetails();
    this.loadHistoricalData();
    this.loadPredictions();
    this.checkWatchlistStatus();
  }

  loadStockDetails(): void {
    this.loading.stock = true;
    this.apiService.getStockDetails(this.symbol, this.exchange).subscribe({
      next: (response: ApiResponse<Stock>) => {
        if (response.success && response.data) {
          this.stock = response.data;
          this.generateStockMetrics();
        }
        this.loading.stock = false;
      },
      error: (error) => {
        console.error('Error loading stock details:', error);
        this.loading.stock = false;
      }
    });
  }

  loadHistoricalData(): void {
    this.loading.chart = true;
    const timeframe = this.timeframes.find(t => t.value === this.selectedTimeframe);
    const period = timeframe?.period || '1mo';

    this.apiService.getHistoricalData(this.symbol, this.exchange, period).subscribe({
      next: (response: ApiResponse<HistoricalData[]>) => {
        if (response.success && response.data) {
          this.historicalData = response.data;
          this.generateChartData();
        }
        this.loading.chart = false;
      },
      error: (error) => {
        console.error('Error loading historical data:', error);
        this.loading.chart = false;
      }
    });
  }

  loadPredictions(): void {
    this.loading.predictions = true;
    this.apiService.getPredictions(this.symbol, this.exchange, 10).subscribe({
      next: (response: ApiResponse<Prediction[]>) => {
        if (response.success && response.data) {
          this.predictions = response.data;
        }
        this.loading.predictions = false;
      },
      error: (error) => {
        console.error('Error loading predictions:', error);
        this.loading.predictions = false;
      }
    });
  }

  checkWatchlistStatus(): void {
    this.apiService.getWatchlistData().subscribe({
      next: (response: ApiResponse<Stock[]>) => {
        if (response.success && response.data) {
          this.isInWatchlist = response.data.some(s =>
            s.symbol === this.symbol && s.exchange === this.exchange
          );
        }
      },
      error: (error) => {
        console.error('Error checking watchlist status:', error);
      }
    });
  }

  generateChartData(): void {
    if (!this.historicalData.length || !this.chartCanvas) return;

    const labels = this.historicalData.map(data =>
      new Date(data.date).toLocaleDateString('en-IN')
    );

    const prices = this.historicalData.map(data => data.close);

    // Destroy existing chart if it exists
    if (this.chart) {
      this.chart.destroy();
    }

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const config: ChartConfiguration = {
      type: 'line' as ChartType,
      data: {
        labels,
        datasets: [{
          label: 'Price (₹)',
          data: prices,
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          fill: true,
          tension: 0.1,
          pointRadius: 2,
          pointHoverRadius: 5
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top'
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: (context) => {
                return `Price: ₹${context.parsed.y.toFixed(2)}`;
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Date'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Price (₹)'
            },
            beginAtZero: false
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    };

    this.chart = new Chart(ctx, config);
  }

  generateStockMetrics(): void {
    if (!this.stock) return;

    // Generate mock metrics - in real app, these would come from API
    this.stockMetrics = {
      marketCap: this.stock.currentPrice * 1000000, // Mock calculation
      peRatio: 15.5 + Math.random() * 10,
      pbRatio: 1.2 + Math.random() * 2,
      dividendYield: Math.random() * 3,
      eps: this.stock.currentPrice / 15,
      bookValue: this.stock.currentPrice * 0.8,
      week52High: this.stock.currentPrice * (1.2 + Math.random() * 0.3),
      week52Low: this.stock.currentPrice * (0.7 + Math.random() * 0.2),
      avgVolume: 1000000 + Math.random() * 5000000,
      beta: 0.8 + Math.random() * 0.8
    };
  }

  onTimeframeChange(event: MatButtonToggleChange): void {
    this.selectedTimeframe = event.value;
    this.loadHistoricalData();
  }

  toggleWatchlist(): void {
    if (!this.stock) return;

    if (this.isInWatchlist) {
      this.apiService.removeFromWatchlist(this.symbol, this.exchange).subscribe({
        next: (response) => {
          if (response.success) {
            this.isInWatchlist = false;
          }
        },
        error: (error) => {
          console.error('Error removing from watchlist:', error);
        }
      });
    } else {
      this.apiService.addToWatchlist(this.symbol, this.exchange).subscribe({
        next: (response) => {
          if (response.success) {
            this.isInWatchlist = true;
          }
        },
        error: (error) => {
          console.error('Error adding to watchlist:', error);
        }
      });
    }
  }

  generatePrediction(): void {
    if (!this.stock) return;

    this.apiService.generatePrediction({
      symbol: this.symbol,
      exchange: this.exchange,
      timeframe: '1d',
      predictionType: 'short_term'
    }).subscribe({
      next: (response) => {
        if (response.success) {
          this.loadPredictions(); // Refresh predictions
        }
      },
      error: (error) => {
        console.error('Error generating prediction:', error);
      }
    });
  }

  startRealTimeUpdates(): void {
    // Update stock price every 30 seconds
    interval(30000)
      .pipe(
        takeUntil(this.destroy$),
        switchMap(() => this.apiService.getCurrentPrice(this.symbol, this.exchange))
      )
      .subscribe({
        next: (response: ApiResponse<Stock>) => {
          if (response.success && response.data && this.stock) {
            this.stock.currentPrice = response.data.currentPrice;
            this.stock.dayChange = response.data.dayChange;
            this.stock.dayChangePercent = response.data.dayChangePercent;
          }
        },
        error: (error) => {
          console.error('Error updating real-time price:', error);
        }
      });
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(value);
  }

  formatPercent(value: number): string {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-IN').format(value);
  }

  getChangeColor(change: number): string {
    if (change > 0) return '#4CAF50';
    if (change < 0) return '#F44336';
    return '#757575';
  }

  goBack(): void {
    this.router.navigate(['/dashboard']);
  }
}
