export interface Stock {
  symbol: string;
  name: string;
  exchange: 'NSE' | 'BSE';
  currentPrice: number;
  dayChange: number;
  dayChangePercent: number;
  volume: number;
  marketCap?: number;
  sector?: string;
  industry?: string;
  lastUpdated: Date;
}

export interface HistoricalData {
  date: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjustedClose?: number;
}

export interface TechnicalIndicators {
  rsi: number;
  macd?: number;
  sma20: number;
  sma50: number;
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  support: number;
  resistance: number;
}

export interface Prediction {
  _id?: string;
  stockSymbol: string;
  exchange: 'NSE' | 'BSE';
  predictionType: 'short_term' | 'medium_term' | 'long_term';
  timeframe: '1d' | '1w' | '1m' | '3m' | '6m' | '1y';
  currentPrice: number;
  predictedPrice: number;
  confidence: number;
  direction: 'bullish' | 'bearish' | 'neutral';
  expectedReturn: number;
  expectedReturnPercent: number;
  riskLevel: 'low' | 'medium' | 'high';
  technicalIndicators?: TechnicalIndicators;
  marketSentiment: 'very_positive' | 'positive' | 'neutral' | 'negative' | 'very_negative';
  newsImpact: 'positive' | 'neutral' | 'negative';
  modelUsed: string;
  modelVersion: string;
  accuracy?: number;
  predictionDate: Date;
  targetDate: Date;
  status: 'pending' | 'completed' | 'expired';
  actualPrice?: number;
  actualReturn?: number;
  predictionAccuracy?: number;
}

export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'user' | 'admin';
  preferences: {
    watchlist: WatchlistItem[];
    defaultExchange: 'NSE' | 'BSE';
    riskTolerance: 'low' | 'medium' | 'high';
    investmentHorizon: 'short_term' | 'medium_term' | 'long_term';
    notifications: {
      email: boolean;
      priceAlerts: boolean;
      predictionUpdates: boolean;
    };
  };
  subscription: {
    plan: 'free' | 'basic' | 'premium';
    startDate?: Date;
    endDate?: Date;
    isActive: boolean;
  };
}

export interface WatchlistItem {
  symbol: string;
  exchange: 'NSE' | 'BSE';
  addedAt: Date;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  count?: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface AuthResponse {
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  user?: User;
  message?: string;
}

export interface PredictionRequest {
  symbol: string;
  exchange?: 'NSE' | 'BSE';
  timeframe?: '1d' | '1w' | '1m' | '3m' | '6m' | '1y';
  predictionType?: 'short_term' | 'medium_term' | 'long_term';
}

export interface StockSearchResult {
  symbol: string;
  name: string;
  exchange: 'NSE' | 'BSE';
}

export interface PredictionStats {
  symbol: string;
  exchange: 'NSE' | 'BSE';
  avgAccuracy: number;
  totalPredictions: number;
  successfulPredictions: number;
  successRate: string;
}
