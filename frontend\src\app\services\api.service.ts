import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError, finalize } from 'rxjs/operators';
import {
  Stock,
  HistoricalData,
  Prediction,
  User,
  ApiResponse,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  PredictionRequest,
  StockSearchResult,
  PredictionStats
} from '../models/stock.model';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = 'http://localhost:3000/api';
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  private sessionInitializedSubject = new BehaviorSubject<boolean>(false);
  public sessionInitialized$ = this.sessionInitializedSubject.asObservable();

  constructor(private http: HttpClient) {
    // Check if user is logged in on service initialization
    this.initializeUserSession();
  }

  private initializeUserSession(): void {
    const accessToken = localStorage.getItem('accessToken');
    const refreshToken = localStorage.getItem('refreshToken');

    if (accessToken && refreshToken) {
      // Try to verify the current token first
      this.verifyToken().subscribe({
        next: () => {
          // Token is valid, user profile will be loaded by verifyToken
          this.sessionInitializedSubject.next(true);
        },
        error: () => {
          // Access token might be expired, try to refresh
          this.refreshToken().subscribe({
            next: () => {
              // Token refreshed successfully, get user profile
              this.getUserProfile().subscribe({
                next: () => {
                  this.sessionInitializedSubject.next(true);
                },
                error: () => {
                  this.clearSession();
                  this.sessionInitializedSubject.next(true);
                }
              });
            },
            error: () => {
              // Both tokens are invalid, clear everything
              this.clearSession();
              this.sessionInitializedSubject.next(true);
            }
          });
        }
      });
    } else if (accessToken && !refreshToken) {
      // Only access token exists, verify it
      this.verifyToken().subscribe({
        next: () => {
          this.sessionInitializedSubject.next(true);
        },
        error: () => {
          this.clearSession();
          this.sessionInitializedSubject.next(true);
        }
      });
    } else {
      // No tokens, session is initialized but user is not logged in
      this.sessionInitializedSubject.next(true);
    }
  }

  private clearSession(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    this.currentUserSubject.next(null);
  }

  private getAuthHeaders(): HttpHeaders {
    const accessToken = localStorage.getItem('accessToken');
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': accessToken ? `Bearer ${accessToken}` : ''
    });
  }

  // Authentication methods
  login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.baseUrl}/auth/login`, credentials)
      .pipe(
        map(response => {
          console.log(response);
          if (response.success && response.accessToken && response.user && response.refreshToken) {
            localStorage.setItem('accessToken', response.accessToken);
            localStorage.setItem('refreshToken', response.refreshToken);
            this.currentUserSubject.next(response.user);
          }
          return response;
        })
      );
  }

  register(userData: RegisterRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.baseUrl}/auth/register`, userData)
      .pipe(
        map(response => {
          if (response.success && response.accessToken && response.user && response.refreshToken) {
            localStorage.setItem('accessToken', response.accessToken);
            localStorage.setItem('refreshToken', response.refreshToken);
            this.currentUserSubject.next(response.user);
          }
          return response;
        })
      );
  }

  logout(): Observable<any> {
    return this.http.post(`${this.baseUrl}/auth/logout`, {}, { headers: this.getAuthHeaders() })
      .pipe(
        finalize(() => {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          this.currentUserSubject.next(null);
        })
      );
  }

  verifyToken(): Observable<any> {
    return this.http.get(`${this.baseUrl}/auth/verify`, { headers: this.getAuthHeaders() })
      .pipe(
        map(response => {
          // If token is valid, get user profile
          this.getUserProfile().subscribe();
          return response;
        })
      );
  }

  refreshToken(): Observable<any> {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    // Use direct HTTP call without interceptor to avoid circular dependency
    return this.http.post<any>(`${this.baseUrl}/auth/refresh`, { refreshToken }, {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    })
      .pipe(
        map(response => {
          if (response.success && response.accessToken) {
            localStorage.setItem('accessToken', response.accessToken);
            localStorage.setItem('refreshToken', response.refreshToken);
          }
          return response;
        })
      );
  }

  getUserProfile(): Observable<ApiResponse<User>> {
    return this.http.get<ApiResponse<User>>(`${this.baseUrl}/auth/profile`, { headers: this.getAuthHeaders() })
      .pipe(
        map(response => {
          if (response.success && response.data) {
            this.currentUserSubject.next(response.data);
          }
          return response;
        })
      );
  }

  updateUserProfile(userData: Partial<User>): Observable<ApiResponse<User>> {
    return this.http.put<ApiResponse<User>>(`${this.baseUrl}/auth/profile`, userData, { headers: this.getAuthHeaders() });
  }

  // Stock methods
  searchStocks(query: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<StockSearchResult[]>> {
    const params = new HttpParams()
      .set('q', query)
      .set('exchange', exchange);

    return this.http.get<ApiResponse<StockSearchResult[]>>(`${this.baseUrl}/stocks/search`, { params });
  }

  getCurrentPrice(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<Stock>> {
    const params = new HttpParams().set('exchange', exchange);
    return this.http.get<ApiResponse<Stock>>(`${this.baseUrl}/stocks/${symbol}/current`, { params });
  }

  getHistoricalData(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE', period: string = '1y'): Observable<ApiResponse<HistoricalData[]>> {
    const params = new HttpParams()
      .set('exchange', exchange)
      .set('period', period);

    return this.http.get<ApiResponse<HistoricalData[]>>(`${this.baseUrl}/stocks/${symbol}/historical`, { params });
  }

  getStockDetails(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<Stock>> {
    const params = new HttpParams().set('exchange', exchange);
    return this.http.get<ApiResponse<Stock>>(`${this.baseUrl}/stocks/${symbol}/details`, { params });
  }

  getTopMovers(type: 'gainers' | 'losers', limit: number = 10): Observable<ApiResponse<Stock[]>> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<ApiResponse<Stock[]>>(`${this.baseUrl}/stocks/movers/${type}`, { params });
  }

  getPopularStocks(limit: number = 20): Observable<ApiResponse<Stock[]>> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<ApiResponse<Stock[]>>(`${this.baseUrl}/stocks/popular`, { params });
  }

  updateStockData(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/stocks/${symbol}/update`,
      { exchange },
      { headers: this.getAuthHeaders() }
    );
  }

  // Watchlist methods
  getWatchlistData(): Observable<ApiResponse<Stock[]>> {
    return this.http.get<ApiResponse<Stock[]>>(`${this.baseUrl}/stocks/watchlist/data`, { headers: this.getAuthHeaders() });
  }

  addToWatchlist(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/auth/watchlist`,
      { symbol, exchange },
      { headers: this.getAuthHeaders() }
    );
  }

  removeFromWatchlist(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<any>> {
    const params = new HttpParams().set('exchange', exchange);
    return this.http.delete<ApiResponse<any>>(`${this.baseUrl}/auth/watchlist/${symbol}`,
      { headers: this.getAuthHeaders(), params }
    );
  }

  // Prediction methods
  generatePrediction(request: PredictionRequest): Observable<ApiResponse<Prediction>> {
    return this.http.post<ApiResponse<Prediction>>(`${this.baseUrl}/predictions/generate`,
      request,
      { headers: this.getAuthHeaders() }
    );
  }

  getPredictions(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE', limit: number = 10): Observable<ApiResponse<Prediction[]>> {
    const params = new HttpParams()
      .set('exchange', exchange)
      .set('limit', limit.toString());

    return this.http.get<ApiResponse<Prediction[]>>(`${this.baseUrl}/predictions/${symbol}`, { params });
  }

  getActivePredictions(symbol?: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<Prediction[]>> {
    const params = new HttpParams().set('exchange', exchange);
    const url = symbol
      ? `${this.baseUrl}/predictions/${symbol}/active`
      : `${this.baseUrl}/predictions/active`;
    return this.http.get<ApiResponse<Prediction[]>>(url, { params, headers: this.getAuthHeaders() });
  }

  getPredictionStats(symbol: string, exchange: 'NSE' | 'BSE' = 'NSE'): Observable<ApiResponse<PredictionStats>> {
    const params = new HttpParams().set('exchange', exchange);
    return this.http.get<ApiResponse<PredictionStats>>(`${this.baseUrl}/predictions/${symbol}/stats`, { params });
  }

  getRecentPredictions(limit: number = 20): Observable<ApiResponse<Prediction[]>> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<ApiResponse<Prediction[]>>(`${this.baseUrl}/predictions`, { params });
  }

  updatePredictionResult(predictionId: string, actualPrice: number): Observable<ApiResponse<any>> {
    return this.http.put<ApiResponse<any>>(`${this.baseUrl}/predictions/${predictionId}/result`,
      { actualPrice },
      { headers: this.getAuthHeaders() }
    );
  }

  batchGeneratePredictions(stocks: { symbol: string; exchange?: 'NSE' | 'BSE' }[], timeframe: string = '1d'): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/predictions/batch`,
      { stocks, timeframe },
      { headers: this.getAuthHeaders() }
    );
  }

  // Additional prediction methods
  getAllPredictions(): Observable<ApiResponse<Prediction[]>> {
    return this.http.get<ApiResponse<Prediction[]>>(`${this.baseUrl}/predictions/all`, { headers: this.getAuthHeaders() });
  }

  getCompletedPredictions(): Observable<ApiResponse<Prediction[]>> {
    return this.http.get<ApiResponse<Prediction[]>>(`${this.baseUrl}/predictions/completed`, { headers: this.getAuthHeaders() });
  }

  deletePrediction(predictionId: string): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.baseUrl}/predictions/${predictionId}`, { headers: this.getAuthHeaders() });
  }

  refreshPrediction(predictionId: string): Observable<ApiResponse<Prediction>> {
    return this.http.put<ApiResponse<Prediction>>(`${this.baseUrl}/predictions/${predictionId}/refresh`, {}, { headers: this.getAuthHeaders() });
  }

  // Utility methods
  isLoggedIn(): boolean {
    return !!localStorage.getItem('accessToken');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }
}
