const axios = require('axios');

async function testPrediction() {
  try {
    // First, try to register a test user
    console.log('Registering test user...');
    try {
      const registerResponse = await axios.post('http://localhost:3000/api/auth/register', {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User'
      });
      console.log('Registration successful');
    } catch (regError) {
      console.log('Registration failed (user might already exist):', regError.response?.data?.message);
    }

    // Now, login to get a token
    console.log('Logging in...');
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (!loginResponse.data.success) {
      console.error('Login failed:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.token;
    console.log('Login successful, token received');

    // Now try to generate a prediction
    console.log('Generating prediction for RELIANCE...');
    const predictionResponse = await axios.post('http://localhost:3000/api/predictions/generate', {
      symbol: 'RELIANCE',
      exchange: 'NSE',
      timeframe: '1d',
      predictionType: 'short_term'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (predictionResponse.data.success) {
      console.log('Prediction generated successfully!');
      console.log('Prediction data:', JSON.stringify(predictionResponse.data.data, null, 2));
    } else {
      console.error('Prediction generation failed:', predictionResponse.data.message);
    }

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testPrediction();
